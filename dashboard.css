/* Dashboard Theme Variables */
:root {
    /* Light Blue Theme (Default) */
    --primary-color: #3498db;       /* Main blue */
    --primary-dark: #2980b9;        /* Darker blue */
    --secondary-color: #2ecc71;     /* Green */
    --secondary-dark: #27ae60;      /* Darker green */
    --accent-color: #e74c3c;        /* Red accent */
    --accent-dark: #c0392b;         /* Darker red */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --bg-color: #ffffff;            /* Background color */
    --text-color: #333333;          /* Text color */
    --sidebar-bg: #f8f9fa;          /* Sidebar background */
    --sidebar-text: #333333;        /* Sidebar text */
    --card-bg: #ffffff;             /* Card background */
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #4dabf7;       /* Brighter blue for dark mode */
    --primary-dark: #339af0;        /* Slightly lighter blue */
    --secondary-color: #51cf66;     /* Brighter green */
    --secondary-dark: #40c057;      /* Slightly lighter green */
    --accent-color: #ff6b6b;        /* Brighter red */
    --accent-dark: #fa5252;         /* Slightly lighter red */
    --neutral-light: #343a40;       /* Dark background */
    --neutral-medium: #495057;      /* Medium dark background */
    --neutral-dark: #dee2e6;        /* Light text */
    --shadow: rgba(0, 0, 0, 0.3);   /* Darker shadow */
    --bg-color: #212529;            /* Dark background */
    --text-color: #f8f9fa;          /* Light text */
    --sidebar-bg: #343a40;          /* Sidebar background */
    --sidebar-text: #f8f9fa;        /* Sidebar text */
    --card-bg: #2c3034;             /* Card background */
}

/* Purple Theme */
[data-theme="purple"] {
    --primary-color: #9775fa;       /* Main purple */
    --primary-dark: #845ef7;        /* Darker purple */
    --secondary-color: #5c7cfa;     /* Blue secondary */
    --secondary-dark: #4c6ef5;      /* Darker blue */
    --accent-color: #ff8787;        /* Coral accent */
    --accent-dark: #ff6b6b;         /* Darker coral */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --bg-color: #ffffff;            /* Background color */
    --text-color: #333333;          /* Text color */
    --sidebar-bg: #f8f0ff;          /* Sidebar background */
    --sidebar-text: #333333;        /* Sidebar text */
    --card-bg: #ffffff;             /* Card background */
}

/* Green Theme */
[data-theme="green"] {
    --primary-color: #40c057;       /* Main green */
    --primary-dark: #37b24d;        /* Darker green */
    --secondary-color: #4dabf7;     /* Blue secondary */
    --secondary-dark: #339af0;      /* Darker blue */
    --accent-color: #ff922b;        /* Orange accent */
    --accent-dark: #fd7e14;         /* Darker orange */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --bg-color: #ffffff;            /* Background color */
    --text-color: #333333;          /* Text color */
    --sidebar-bg: #f1f9f1;          /* Sidebar background */
    --sidebar-text: #333333;        /* Sidebar text */
    --card-bg: #ffffff;             /* Card background */
}

/* Dark Blue Theme */
[data-theme="dark-blue"] {
    --primary-color: #228be6;       /* Main blue */
    --primary-dark: #1c7ed6;        /* Darker blue */
    --secondary-color: #15aabf;     /* Teal secondary */
    --secondary-dark: #1098ad;      /* Darker teal */
    --accent-color: #f76707;        /* Orange accent */
    --accent-dark: #e8590c;         /* Darker orange */
    --neutral-light: #343a40;       /* Dark background */
    --neutral-medium: #495057;      /* Medium dark background */
    --neutral-dark: #dee2e6;        /* Light text */
    --shadow: rgba(0, 0, 0, 0.3);   /* Darker shadow */
    --bg-color: #1e2a3a;            /* Dark blue background */
    --text-color: #f8f9fa;          /* Light text */
    --sidebar-bg: #172331;          /* Sidebar background */
    --sidebar-text: #f8f9fa;        /* Sidebar text */
    --card-bg: #263a4e;             /* Card background */
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    transition: all 0.3s ease;
    height: 100vh;
    overflow: hidden;
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    height: 100vh;
    width: 100%;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    border-right: 1px solid var(--neutral-medium);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    overflow-y: auto;
}

.sidebar.collapsed {
    width: 60px;
}

.sidebar-header {
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--neutral-medium);
}

.sidebar.collapsed .sidebar-header h2 {
    display: none;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--primary-color);
    cursor: pointer;
    font-size: 1.2rem;
}

.sidebar-nav {
    padding: 20px 0;
    flex-grow: 1;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--sidebar-text);
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 5px;
    margin: 0 10px;
}

.sidebar-nav li.active a {
    background-color: var(--primary-color);
    color: white;
}

.sidebar-nav a:hover {
    background-color: var(--neutral-medium);
}

.sidebar-nav li.active a:hover {
    background-color: var(--primary-dark);
}

.sidebar-nav i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed .sidebar-nav span {
    display: none;
}

.sidebar .theme-selector {
    padding: 20px;
    border-top: 1px solid var(--neutral-medium);
}

.sidebar.collapsed .theme-selector {
    display: none;
}

.theme-selector label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
}

.theme-selector select {
    width: 100%;
    padding: 8px;
    border-radius: 4px;
    border: 1px solid var(--neutral-medium);
    background-color: var(--bg-color);
    color: var(--text-color);
}

/* Main Content Area */
.main-content {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.content-header {
    padding: 20px;
    border-bottom: 1px solid var(--neutral-medium);
}

.content-container {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px;
    position: relative;
}

/* Page Content */
.page-content {
    display: none;
    height: 100%;
}

.page-content.active {
    display: block;
}

/* Dashboard Home */
.dashboard-welcome {
    text-align: center;
    margin-bottom: 40px;
}

.dashboard-welcome h2 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

/* Tool Cards */
.tool-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.tool-card {
    background-color: var(--card-bg);
    border-radius: 8px;
    box-shadow: 0 4px 6px var(--shadow);
    overflow: hidden;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    cursor: pointer;
}

.tool-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px var(--shadow);
}

.tool-card-icon {
    width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: var(--primary-color);
    color: white;
    font-size: 2rem;
}

.tool-card-content {
    padding: 20px;
    flex-grow: 1;
}

.tool-card h3 {
    color: var(--primary-color);
    margin-bottom: 10px;
}

.tool-launch-btn {
    margin-top: 15px;
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.tool-launch-btn:hover {
    background-color: var(--primary-dark);
}

/* Tool Containers */
.tool-container {
    height: 100%;
    border-radius: 8px;
    overflow: hidden;
    background-color: var(--card-bg);
    box-shadow: 0 4px 6px var(--shadow);
}

/* iFrame Styling */
iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.content-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 999;
}

.content-overlay.active {
    display: block;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        position: fixed;
        left: -250px;
        top: 0;
        bottom: 0;
        z-index: 1000;
        height: 100%;
        box-shadow: 0 0 15px rgba(0,0,0,0.2);
    }

    .sidebar.collapsed {
        left: 0;
        width: 250px; /* Full width when expanded on mobile */
    }

    .sidebar.collapsed .sidebar-header h2,
    .sidebar.collapsed .sidebar-nav span,
    .sidebar.collapsed .theme-selector {
        display: block; /* Show text when expanded */
    }

    .main-content {
        margin-left: 0;
    }

    .sidebar-toggle {
        display: block; /* Always show toggle on mobile */
    }

    .tool-cards {
        grid-template-columns: 1fr; /* Stack cards on mobile */
    }
}
