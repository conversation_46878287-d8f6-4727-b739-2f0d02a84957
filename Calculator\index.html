<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced Calculator</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- MathJS for advanced calculations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mathjs/11.8.0/math.min.js"></script>
</head>
<body>
    <div class="calculator-container">
        <h1>Advanced Calculator</h1>
        
        <div class="calculator">
            <div class="display-container">
                <div class="history" id="history"></div>
                <div class="display">
                    <div class="expression" id="expression"></div>
                    <div class="result" id="result">0</div>
                </div>
            </div>
            
            <div class="buttons-container">
                <!-- Memory and Clear Buttons -->
                <div class="button-row">
                    <button class="btn function-btn" data-action="memory-clear">MC</button>
                    <button class="btn function-btn" data-action="memory-recall">MR</button>
                    <button class="btn function-btn" data-action="memory-add">M+</button>
                    <button class="btn function-btn" data-action="memory-subtract">M-</button>
                    <button class="btn function-btn" data-action="memory-store">MS</button>
                </div>
                
                <!-- Scientific Functions -->
                <div class="button-row">
                    <button class="btn function-btn" data-action="square">x²</button>
                    <button class="btn function-btn" data-action="cube">x³</button>
                    <button class="btn function-btn" data-action="power">x^y</button>
                    <button class="btn function-btn" data-action="sqrt">√</button>
                    <button class="btn function-btn" data-action="cbrt">∛</button>
                </div>
                
                <div class="button-row">
                    <button class="btn function-btn" data-action="sin">sin</button>
                    <button class="btn function-btn" data-action="cos">cos</button>
                    <button class="btn function-btn" data-action="tan">tan</button>
                    <button class="btn function-btn" data-action="log">log</button>
                    <button class="btn function-btn" data-action="ln">ln</button>
                </div>
                
                <div class="button-row">
                    <button class="btn function-btn" data-action="factorial">x!</button>
                    <button class="btn function-btn" data-action="percent">%</button>
                    <button class="btn function-btn" data-action="pi">π</button>
                    <button class="btn function-btn" data-action="e">e</button>
                    <button class="btn function-btn" data-action="abs">|x|</button>
                </div>
                
                <!-- Main Calculator Buttons -->
                <div class="button-row">
                    <button class="btn clear-btn" data-action="clear">C</button>
                    <button class="btn clear-btn" data-action="backspace"><i class="fas fa-backspace"></i></button>
                    <button class="btn operator-btn" data-action="(">(</button>
                    <button class="btn operator-btn" data-action=")">)</button>
                    <button class="btn operator-btn" data-action="/">/</button>
                </div>
                
                <div class="button-row">
                    <button class="btn number-btn" data-action="7">7</button>
                    <button class="btn number-btn" data-action="8">8</button>
                    <button class="btn number-btn" data-action="9">9</button>
                    <button class="btn operator-btn" data-action="*">×</button>
                    <button class="btn operator-btn" data-action="mod">mod</button>
                </div>
                
                <div class="button-row">
                    <button class="btn number-btn" data-action="4">4</button>
                    <button class="btn number-btn" data-action="5">5</button>
                    <button class="btn number-btn" data-action="6">6</button>
                    <button class="btn operator-btn" data-action="-">−</button>
                    <button class="btn operator-btn" data-action="1/x">1/x</button>
                </div>
                
                <div class="button-row">
                    <button class="btn number-btn" data-action="1">1</button>
                    <button class="btn number-btn" data-action="2">2</button>
                    <button class="btn number-btn" data-action="3">3</button>
                    <button class="btn operator-btn" data-action="+">+</button>
                    <button class="btn equals-btn" data-action="=">=</button>
                </div>
                
                <div class="button-row">
                    <button class="btn number-btn" data-action="0">0</button>
                    <button class="btn number-btn" data-action=".">.</button>
                    <button class="btn number-btn" data-action="exp">EXP</button>
                    <button class="btn function-btn" data-action="ans">ANS</button>
                    <button class="btn function-btn" data-action="toggle-history"><i class="fas fa-history"></i></button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html>
