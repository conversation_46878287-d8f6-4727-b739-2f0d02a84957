<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Time Calculator</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="calculator-container">
        <!-- Theme selector removed - now controlled by dashboard -->
        <h1>Payroll Time Calculator</h1>

        <!-- Tab Navigation -->
        <div class="tab-container" role="tablist" aria-label="Time Conversion Tabs">
            <button class="tab-button active" id="tab-time-to-decimal" role="tab" aria-selected="true" aria-controls="panel-time-to-decimal">
                Time to Decimal
            </button>
            <button class="tab-button" id="tab-decimal-to-time" role="tab" aria-selected="false" aria-controls="panel-decimal-to-time" tabindex="-1">
                Decimal to Time
            </button>
        </div>

        <!-- Tab Content Panels -->
        <div class="tab-content-container">
            <!-- Panel 1: Time to Decimal -->
            <div class="tab-panel active" id="panel-time-to-decimal" role="tabpanel" aria-labelledby="tab-time-to-decimal">
                <p class="description">Enter time (HH:MM, less than 24:00). Colon ':' can be typed manually or added automatically.</p>
                <div class="input-group">
                    <label for="timeInput">Time (HH:MM):</label>
                    <input type="text" id="timeInput" placeholder="e.g., 07:36 or 23:59" maxlength="5" aria-label="Time in HH:MM format, type up to 4 digits or HH:MM">
                </div>
                <!-- Real-time Checkbox -->
                <div class="checkbox-group">
                    <input type="checkbox" id="realtimeCheckTime" name="realtimeCheckTime">
                    <label for="realtimeCheckTime">Calculate in Real-time</label>
                </div>
                <button id="calculateDecimalBtn">Calculate Decimal</button>
                <div class="result-area" aria-live="polite">
                    <h3>Result (Decimal):</h3>
                    <span id="decimalResult" class="result-value">--</span>
                </div>
                <div id="error-message-time" class="error-message" role="alert"></div>
            </div>

            <!-- Panel 2: Decimal to Time -->
            <div class="tab-panel" id="panel-decimal-to-time" role="tabpanel" aria-labelledby="tab-decimal-to-time" hidden>
                <p class="description">Enter decimal time (e.g., 7.6). Must be less than 24.00.</p>
                <div class="input-group">
                    <label for="decimalInput">Decimal Value (< 24.00):</label>
                    <input type="text" id="decimalInput" placeholder="e.g., 7.6 or 23.99" inputmode="decimal" aria-label="Decimal time value, must be less than 24">
                </div>
                 <!-- Real-time Checkbox -->
                <div class="checkbox-group">
                    <input type="checkbox" id="realtimeCheckDecimal" name="realtimeCheckDecimal">
                    <label for="realtimeCheckDecimal">Calculate in Real-time</label>
                </div>
                <button id="calculateTimeBtn">Calculate Time (HH:MM)</button>
                <div class="result-area" aria-live="polite">
                    <h3>Result (HH:MM):</h3>
                    <span id="timeResult" class="result-value">--:--</span>
                </div>
                <div id="error-message-decimal" class="error-message" role="alert"></div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>