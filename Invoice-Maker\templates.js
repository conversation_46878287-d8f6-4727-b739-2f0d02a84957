// Invoice Templates
const templates = {
    // Classic Template
    classic: {
        name: 'Classic',
        description: 'A clean, professional invoice template.',

        // Generate HTML for preview
        generateHtml: function(data) {
            return `
                <div class="invoice-preview classic-template">
                    <div class="invoice-header">
                        <div class="invoice-title">
                            <h1>INVOICE</h1>
                            <div class="invoice-number">#${data.invoiceNumber || 'New Invoice'}</div>
                        </div>
                        <div class="invoice-company">
                            <h2>${data.business.name || 'Your Business'}</h2>
                            <p>${data.business.address || ''}</p>
                            <p>${data.business.phone || ''}</p>
                            <p>${data.business.email || ''}</p>
                            <p>${data.business.website || ''}</p>
                        </div>
                    </div>

                    <div class="invoice-info">
                        <div class="client-info">
                            <h3>Bill To:</h3>
                            <h4>${data.client.name || 'Client Name'}</h4>
                            <p>${data.client.address || ''}</p>
                            <p>${data.client.phone || ''}</p>
                            <p>${data.client.email || ''}</p>
                        </div>
                        <div class="invoice-details">
                            <div class="detail-row">
                                <span>Invoice Date:</span>
                                <span>${data.invoiceDate || ''}</span>
                            </div>
                            <div class="detail-row">
                                <span>Due Date:</span>
                                <span>${data.dueDate || ''}</span>
                            </div>
                        </div>
                    </div>

                    <div class="invoice-items">
                        <table>
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Tax (%)</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.items.map(item => `
                                    <tr>
                                        <td>${item.description || 'Item'}</td>
                                        <td>${item.quantity}</td>
                                        <td>${data.currencySymbol}${item.price.toFixed(2)}</td>
                                        <td>${item.taxRate}%</td>
                                        <td>${data.currencySymbol}${item.amount.toFixed(2)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="invoice-summary">
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span>${data.currencySymbol}${data.subtotal.toFixed(2)}</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax:</span>
                            <span>${data.currencySymbol}${data.taxTotal.toFixed(2)}</span>
                        </div>
                        <div class="summary-row total">
                            <span>Total:</span>
                            <span>${data.currencySymbol}${data.grandTotal.toFixed(2)}</span>
                        </div>
                    </div>

                    ${data.notes ? `
                        <div class="invoice-notes">
                            <h3>${data.notesTitle || 'Notes'}</h3>
                            <p>${data.notes}</p>
                        </div>
                    ` : ''}

                    <div class="invoice-footer">
                        <p>Thank you for your business!</p>
                    </div>
                </div>
            `;
        },

        // Generate PDF
        generatePdf: function(doc, data) {
            // Set font
            doc.setFont('helvetica');

            // Header
            doc.setFontSize(24);
            doc.setTextColor(41, 128, 185); // Blue color
            doc.text('INVOICE', 20, 20);

            doc.setFontSize(12);
            doc.setTextColor(100, 100, 100);
            doc.text(`${data.invoiceNumber || 'New Invoice'}`, 20, 27);

            // Business info
            doc.setFontSize(14);
            doc.setTextColor(0, 0, 0);
            doc.text(data.business.name || 'Your Business', 120, 20);

            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100);

            // Handle business info with dynamic positioning to avoid gaps
            let businessInfoY = 27;
            const lineSpacing = 5;

            if (data.business.address && data.business.address.trim()) {
                doc.text(data.business.address.split('\\n'), 120, businessInfoY);
                businessInfoY += data.business.address.split('\\n').length * lineSpacing;
            }

            if (data.business.phone && data.business.phone.trim()) {
                doc.text(data.business.phone, 120, businessInfoY);
                businessInfoY += lineSpacing;
            }

            if (data.business.email && data.business.email.trim()) {
                doc.text(data.business.email, 120, businessInfoY);
                businessInfoY += lineSpacing;
            }

            if (data.business.website && data.business.website.trim()) {
                doc.text(data.business.website, 120, businessInfoY);
            }

            // Client info
            doc.setFontSize(12);
            doc.setTextColor(41, 128, 185);
            doc.text('Bill To:', 20, 50);

            doc.setFontSize(11);
            doc.setTextColor(0, 0, 0);
            doc.text(data.client.name || 'Client Name', 20, 57);

            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100);

            // Handle client info with dynamic positioning to avoid gaps
            let clientInfoY = 64;

            if (data.client.address && data.client.address.trim()) {
                doc.text(data.client.address.split('\\n'), 20, clientInfoY);
                clientInfoY += data.client.address.split('\\n').length * lineSpacing;
            }

            if (data.client.phone && data.client.phone.trim()) {
                doc.text(data.client.phone, 20, clientInfoY);
                clientInfoY += lineSpacing;
            }

            if (data.client.email && data.client.email.trim()) {
                doc.text(data.client.email, 20, clientInfoY);
            }

            // Invoice details
            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);
            doc.text('Invoice Date:', 120, 57);
            doc.text(data.invoiceDate || '', 160, 57);

            doc.text('Due Date:', 120, 64);
            doc.text(data.dueDate || '', 160, 64);

            // Items table
            const tableTop = 100; // Increased from 90 to add more space
            const tableHeaders = ['Description', 'Qty', 'Unit Price', 'Tax (%)', 'Amount'];
            const tableData = data.items.map(item => [
                item.description || 'Item',
                item.quantity.toString(),
                `${item.price.toFixed(2)}`,
                `${item.taxRate}%`,
                `${item.amount.toFixed(2)}`
            ]);

            // Draw table headers
            doc.setFillColor(240, 240, 240);
            doc.rect(20, tableTop - 10, 170, 10, 'F');

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);
            doc.text(tableHeaders[0], 22, tableTop - 3);
            doc.text(tableHeaders[1], 80, tableTop - 3);
            doc.text(tableHeaders[2], 100, tableTop - 3);
            doc.text(tableHeaders[3], 130, tableTop - 3);
            doc.text(tableHeaders[4], 160, tableTop - 3);

            // Draw table rows
            let yPos = tableTop;
            tableData.forEach((row, i) => {
                // Adjust row height for multi-line descriptions
                const descLines = row[0].split('\n');
                const lineCount = Math.min(descLines.length, 6); // Increased from 3 to 6 lines
                const rowHeight = Math.max(10, lineCount * 5 + 5); // Base height + additional lines

                // Alternate row background
                if (i % 2 === 1) {
                    doc.setFillColor(250, 250, 250);
                    doc.rect(20, yPos, 170, rowHeight, 'F');
                }

                // Handle multi-line description
                let lineHeight = 5;

                // Display first line
                doc.text(descLines[0].substring(0, 30), 22, yPos + lineHeight);

                // Display additional lines if any
                for (let i = 1; i < descLines.length && i < 6; i++) { // Increased from 3 to 6 lines
                    lineHeight += 5;
                    doc.text(descLines[i].substring(0, 30), 22, yPos + lineHeight);
                }
                doc.text(row[1], 80, yPos + 5);
                doc.text(row[2], 100, yPos + 5);
                doc.text(row[3], 130, yPos + 5);
                doc.text(row[4], 160, yPos + 5);

                yPos += rowHeight;
            });

            // Draw table border
            doc.setDrawColor(200, 200, 200);
            doc.rect(20, tableTop - 10, 170, yPos - tableTop + 10, 'S');

            // Summary
            const summaryX = 130;
            let summaryY = yPos + 20;

            // Get the summary spacing and alignment from settings or use defaults
            const settings = JSON.parse(localStorage.getItem('invoice-maker-settings') || '{}');
            const summarySpacing = settings.summarySpacing || 40; // Default to 40mm if not set
            const summaryAlignment = settings.summaryAlignment || 'right'; // Default to right alignment

            // Calculate the position for the amount based on the user's spacing preference
            const amountX = summaryX + summarySpacing;

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);

            // Format all amounts to ensure consistent decimal places
            const subtotalText = `${data.subtotal.toFixed(2)}`;
            const taxText = `${data.taxTotal.toFixed(2)}`;
            const totalText = `Total ${data.currency}:`;
            const grandTotalText = `${data.grandTotal.toFixed(2)}`;

            // Subtotal row
            doc.text('Subtotal:', summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(subtotalText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(subtotalText, amountX, summaryY);
            }

            summaryY += 7;

            // Tax row
            doc.text('Tax:', summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(taxText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(taxText, amountX, summaryY);
            }

            summaryY += 7;
            doc.setFontSize(12);
            doc.setTextColor(41, 128, 185);

            // Total row
            doc.text(totalText, summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(grandTotalText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(grandTotalText, amountX, summaryY);
            }

            // Notes
            if (data.notes) {
                summaryY += 20;
                doc.setFontSize(11);
                doc.setTextColor(0, 0, 0);
                doc.text(`${data.notesTitle || 'Notes'}:`, 20, summaryY);

                summaryY += 7;
                doc.setFontSize(10);
                doc.setTextColor(100, 100, 100);
                // Handle multi-line notes properly
                const notesLines = data.notes.split(/\r?\n/);
                let noteY = summaryY;

                // Display each line of notes
                notesLines.forEach(line => {
                    doc.text(line, 20, noteY);
                    noteY += 5; // Increment Y position for next line
                });
            }

            // Footer
            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100);
            doc.text('Thank you for your business!', 105, 280, { align: 'center' });
        }
    },

    // Modern Template
    modern: {
        name: 'Modern',
        description: 'A sleek, contemporary design with accent colors.',

        // Generate HTML for preview
        generateHtml: function(data) {
            return `
                <div class="invoice-preview modern-template">
                    <div class="invoice-header">
                        <div class="invoice-title">
                            <h1>INVOICE</h1>
                            <div class="invoice-number">#${data.invoiceNumber || 'New Invoice'}</div>
                        </div>
                        <div class="invoice-company">
                            <h2>${data.business.name || 'Your Business'}</h2>
                            <p>${data.business.address || ''}</p>
                            <p>${data.business.phone || ''}</p>
                            <p>${data.business.email || ''}</p>
                            <p>${data.business.website || ''}</p>
                        </div>
                    </div>

                    <div class="invoice-info">
                        <div class="client-info">
                            <h3>Bill To:</h3>
                            <h4>${data.client.name || 'Client Name'}</h4>
                            <p>${data.client.address || ''}</p>
                            <p>${data.client.phone || ''}</p>
                            <p>${data.client.email || ''}</p>
                        </div>
                        <div class="invoice-details">
                            <div class="detail-row">
                                <span>Invoice Date:</span>
                                <span>${data.invoiceDate || ''}</span>
                            </div>
                            <div class="detail-row">
                                <span>Due Date:</span>
                                <span>${data.dueDate || ''}</span>
                            </div>
                        </div>
                    </div>

                    <div class="invoice-items">
                        <table>
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Tax (%)</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.items.map(item => `
                                    <tr>
                                        <td>${item.description || 'Item'}</td>
                                        <td>${item.quantity}</td>
                                        <td>${data.currencySymbol}${item.price.toFixed(2)}</td>
                                        <td>${item.taxRate}%</td>
                                        <td>${data.currencySymbol}${item.amount.toFixed(2)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="invoice-summary">
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span>${data.currencySymbol}${data.subtotal.toFixed(2)}</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax:</span>
                            <span>${data.currencySymbol}${data.taxTotal.toFixed(2)}</span>
                        </div>
                        <div class="summary-row total">
                            <span>Total:</span>
                            <span>${data.currencySymbol}${data.grandTotal.toFixed(2)}</span>
                        </div>
                    </div>

                    ${data.notes ? `
                        <div class="invoice-notes">
                            <h3>${data.notesTitle || 'Notes'}</h3>
                            <p>${data.notes}</p>
                        </div>
                    ` : ''}

                    <div class="invoice-footer">
                        <p>Thank you for your business!</p>
                    </div>
                </div>
            `;
        },

        // Generate PDF
        generatePdf: function(doc, data) {
            // Similar to classic but with modern styling
            // Set font
            doc.setFont('helvetica');

            // Add a colored header bar
            doc.setFillColor(52, 152, 219); // Blue color
            doc.rect(0, 0, doc.internal.pageSize.width, 40, 'F');

            // Header
            doc.setFontSize(24);
            doc.setTextColor(255, 255, 255); // White color
            doc.text('INVOICE', 20, 25);

            doc.setFontSize(12);
            doc.text(`${data.invoiceNumber || 'New Invoice'}`, 20, 32);

            // Business info
            doc.setFontSize(14);
            doc.text(data.business.name || 'Your Business', 120, 20);

            doc.setFontSize(10);

            // Handle business info with dynamic positioning to avoid gaps
            let businessInfoY = 27;
            const lineSpacing = 5;

            if (data.business.address && data.business.address.trim()) {
                doc.text(data.business.address.split('\\n'), 120, businessInfoY);
                businessInfoY += data.business.address.split('\\n').length * lineSpacing;
            }

            if (data.business.phone && data.business.phone.trim()) {
                doc.text(data.business.phone, 120, businessInfoY);
                businessInfoY += lineSpacing;
            }

            if (data.business.email && data.business.email.trim()) {
                doc.text(data.business.email, 120, businessInfoY);
                businessInfoY += lineSpacing;
            }

            if (data.business.website && data.business.website.trim()) {
                doc.text(data.business.website, 120, businessInfoY);
            }

            // Client info
            doc.setFontSize(12);
            doc.setTextColor(52, 152, 219); // Blue color
            doc.text('Bill To:', 20, 60);

            doc.setFontSize(11);
            doc.setTextColor(0, 0, 0);
            doc.text(data.client.name || 'Client Name', 20, 67);

            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100);

            // Handle client info with dynamic positioning to avoid gaps
            let clientInfoY = 74;

            if (data.client.address && data.client.address.trim()) {
                doc.text(data.client.address.split('\\n'), 20, clientInfoY);
                clientInfoY += data.client.address.split('\\n').length * lineSpacing;
            }

            if (data.client.phone && data.client.phone.trim()) {
                doc.text(data.client.phone, 20, clientInfoY);
                clientInfoY += lineSpacing;
            }

            if (data.client.email && data.client.email.trim()) {
                doc.text(data.client.email, 20, clientInfoY);
            }

            // Invoice details
            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);
            doc.text('Invoice Date:', 120, 67);
            doc.text(data.invoiceDate || '', 160, 67);

            doc.text('Due Date:', 120, 74);
            doc.text(data.dueDate || '', 160, 74);

            // Items table
            const tableTop = 100;
            const tableHeaders = ['Description', 'Qty', 'Unit Price', 'Tax (%)', 'Amount'];
            const tableData = data.items.map(item => [
                item.description || 'Item',
                item.quantity.toString(),
                `${item.price.toFixed(2)}`,
                `${item.taxRate}%`,
                `${item.amount.toFixed(2)}`
            ]);

            // Draw table headers
            doc.setFillColor(52, 152, 219); // Blue color
            doc.rect(20, tableTop - 10, 170, 10, 'F');

            doc.setFontSize(10);
            doc.setTextColor(255, 255, 255); // White text
            doc.text(tableHeaders[0], 22, tableTop - 3);
            doc.text(tableHeaders[1], 80, tableTop - 3);
            doc.text(tableHeaders[2], 100, tableTop - 3);
            doc.text(tableHeaders[3], 130, tableTop - 3);
            doc.text(tableHeaders[4], 160, tableTop - 3);

            // Draw table rows
            let yPos = tableTop;
            tableData.forEach((row, i) => {
                // Adjust row height for multi-line descriptions
                const descLines = row[0].split('\n');
                const lineCount = Math.min(descLines.length, 6); // Increased from 3 to 6 lines
                const rowHeight = Math.max(10, lineCount * 5 + 5); // Base height + additional lines

                // Alternate row background
                if (i % 2 === 1) {
                    doc.setFillColor(240, 240, 240);
                    doc.rect(20, yPos, 170, rowHeight, 'F');
                }

                // Handle multi-line description
                let lineHeight = 5;

                // Display first line
                doc.setTextColor(0, 0, 0);
                doc.text(descLines[0].substring(0, 30), 22, yPos + lineHeight);

                // Display additional lines if any
                for (let i = 1; i < descLines.length && i < 6; i++) { // Increased from 3 to 6 lines
                    lineHeight += 5;
                    doc.text(descLines[i].substring(0, 30), 22, yPos + lineHeight);
                }
                doc.text(row[1], 80, yPos + 5);
                doc.text(row[2], 100, yPos + 5);
                doc.text(row[3], 130, yPos + 5);
                doc.text(row[4], 160, yPos + 5);

                yPos += rowHeight;
            });

            // Draw table border
            doc.setDrawColor(200, 200, 200);
            doc.rect(20, tableTop - 10, 170, yPos - tableTop + 10, 'S');

            // Summary
            const summaryX = 130;
            let summaryY = yPos + 20;

            // Get the summary spacing and alignment from settings or use defaults
            const settings = JSON.parse(localStorage.getItem('invoice-maker-settings') || '{}');
            const summarySpacing = settings.summarySpacing || 40; // Default to 40mm if not set
            const summaryAlignment = settings.summaryAlignment || 'right'; // Default to right alignment

            // Calculate the position for the amount based on the user's spacing preference
            const amountX = summaryX + summarySpacing;

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);

            // Format all amounts to ensure consistent decimal places
            const subtotalText = `${data.subtotal.toFixed(2)}`;
            const taxText = `${data.taxTotal.toFixed(2)}`;
            const totalText = `Total ${data.currency}:`;
            const grandTotalText = `${data.grandTotal.toFixed(2)}`;

            // Subtotal row
            doc.text('Subtotal:', summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(subtotalText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(subtotalText, amountX, summaryY);
            }

            summaryY += 7;

            // Tax row
            doc.text('Tax:', summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(taxText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(taxText, amountX, summaryY);
            }

            summaryY += 7;
            doc.setFontSize(12);
            doc.setTextColor(52, 152, 219); // Blue color

            // Total row
            doc.text(totalText, summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(grandTotalText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(grandTotalText, amountX, summaryY);
            }

            // Notes
            if (data.notes) {
                summaryY += 20;
                doc.setFontSize(11);
                doc.setTextColor(0, 0, 0);
                doc.text(`${data.notesTitle || 'Notes'}:`, 20, summaryY);

                summaryY += 7;
                doc.setFontSize(10);
                doc.setTextColor(100, 100, 100);

                // Handle multi-line notes properly
                const notesLines = data.notes.split(/\r?\n/);
                let noteY = summaryY;

                // Display each line of notes
                notesLines.forEach(line => {
                    doc.text(line, 20, noteY);
                    noteY += 5; // Increment Y position for next line
                });
            }

            // Footer
            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100);
            doc.text('Thank you for your business!', 105, 280, { align: 'center' });
        }
    },

    // Minimal Template
    minimal: {
        name: 'Minimal',
        description: 'A simple, minimalist design focused on clarity.',

        // Generate HTML for preview
        generateHtml: function(data) {
            return `
                <div class="invoice-preview minimal-template">
                    <div class="invoice-header">
                        <div class="invoice-title">
                            <h1>INVOICE</h1>
                            <div class="invoice-number">${data.invoiceNumber || 'New Invoice'}</div>
                        </div>
                        <div class="invoice-company">
                            <h2>${data.business.name || 'Your Business'}</h2>
                            <p>${data.business.address || ''}</p>
                            <p>${data.business.phone || ''}</p>
                            <p>${data.business.email || ''}</p>
                            <p>${data.business.website || ''}</p>
                        </div>
                    </div>

                    <div class="invoice-info">
                        <div class="client-info">
                            <h3>Bill To:</h3>
                            <h4>${data.client.name || 'Client Name'}</h4>
                            <p>${data.client.address || ''}</p>
                            <p>${data.client.phone || ''}</p>
                            <p>${data.client.email || ''}</p>
                        </div>
                        <div class="invoice-details">
                            <div class="detail-row">
                                <span>Invoice Date:</span>
                                <span>${data.invoiceDate || ''}</span>
                            </div>
                            <div class="detail-row">
                                <span>Due Date:</span>
                                <span>${data.dueDate || ''}</span>
                            </div>
                        </div>
                    </div>

                    <div class="invoice-items">
                        <table class="minimal-table">
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Tax (%)</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.items.map(item => `
                                    <tr>
                                        <td>${item.description || 'Item'}</td>
                                        <td>${item.quantity}</td>
                                        <td>${data.currencySymbol}${item.price.toFixed(2)}</td>
                                        <td>${item.taxRate}%</td>
                                        <td>${data.currencySymbol}${item.amount.toFixed(2)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="invoice-summary">
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span>${data.currencySymbol}${data.subtotal.toFixed(2)}</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax:</span>
                            <span>${data.currencySymbol}${data.taxTotal.toFixed(2)}</span>
                        </div>
                        <div class="summary-row total">
                            <span>Total:</span>
                            <span>${data.currencySymbol}${data.grandTotal.toFixed(2)}</span>
                        </div>
                    </div>

                    ${data.notes ? `
                        <div class="invoice-notes">
                            <h3>${data.notesTitle || 'Notes'}</h3>
                            <p>${data.notes}</p>
                        </div>
                    ` : ''}

                    <div class="invoice-footer">
                        <p>Thank you for your business!</p>
                    </div>
                </div>
            `;
        },

        // Generate PDF
        generatePdf: function(doc, data) {
            // Set font
            doc.setFont('helvetica');

            // Header
            doc.setFontSize(20);
            doc.setTextColor(0, 0, 0); // Black color
            doc.text('INVOICE', 20, 20);

            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100);
            doc.text(`${data.invoiceNumber || 'New Invoice'}`, 20, 27);

            // Business info
            doc.setFontSize(12);
            doc.setTextColor(0, 0, 0);
            doc.text(data.business.name || 'Your Business', 120, 20);

            doc.setFontSize(9);
            doc.setTextColor(100, 100, 100);

            // Handle business info with dynamic positioning to avoid gaps
            let businessInfoY = 27;
            const lineSpacing = 5;

            if (data.business.address && data.business.address.trim()) {
                doc.text(data.business.address.split('\n'), 120, businessInfoY);
                businessInfoY += data.business.address.split('\n').length * lineSpacing;
            }

            if (data.business.phone && data.business.phone.trim()) {
                doc.text(data.business.phone, 120, businessInfoY);
                businessInfoY += lineSpacing;
            }

            if (data.business.email && data.business.email.trim()) {
                doc.text(data.business.email, 120, businessInfoY);
                businessInfoY += lineSpacing;
            }

            if (data.business.website && data.business.website.trim()) {
                doc.text(data.business.website, 120, businessInfoY);
            }

            // Client info
            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);
            doc.text('Bill To:', 20, 50);

            doc.setFontSize(10);
            doc.text(data.client.name || 'Client Name', 20, 57);

            doc.setFontSize(9);
            doc.setTextColor(100, 100, 100);

            // Handle client info with dynamic positioning to avoid gaps
            let clientInfoY = 64;

            if (data.client.address && data.client.address.trim()) {
                doc.text(data.client.address.split('\n'), 20, clientInfoY);
                clientInfoY += data.client.address.split('\n').length * lineSpacing;
            }

            if (data.client.phone && data.client.phone.trim()) {
                doc.text(data.client.phone, 20, clientInfoY);
                clientInfoY += lineSpacing;
            }

            if (data.client.email && data.client.email.trim()) {
                doc.text(data.client.email, 20, clientInfoY);
            }

            // Invoice details
            doc.setFontSize(9);
            doc.setTextColor(0, 0, 0);
            doc.text('Invoice Date:', 120, 57);
            doc.text(data.invoiceDate || '', 160, 57);

            doc.text('Due Date:', 120, 64);
            doc.text(data.dueDate || '', 160, 64);

            // Items table
            const tableTop = 100;
            const tableHeaders = ['Description', 'Qty', 'Unit Price', 'Tax (%)', 'Amount'];
            const tableData = data.items.map(item => [
                item.description || 'Item',
                item.quantity.toString(),
                `${item.price.toFixed(2)}`,
                `${item.taxRate}%`,
                `${item.amount.toFixed(2)}`
            ]);

            // Draw table headers
            doc.setFontSize(9);
            doc.setTextColor(0, 0, 0);
            doc.text(tableHeaders[0], 22, tableTop - 3);
            doc.text(tableHeaders[1], 80, tableTop - 3);
            doc.text(tableHeaders[2], 100, tableTop - 3);
            doc.text(tableHeaders[3], 130, tableTop - 3);
            doc.text(tableHeaders[4], 160, tableTop - 3);

            // Draw horizontal line under headers
            doc.setDrawColor(200, 200, 200);
            doc.line(20, tableTop, 190, tableTop);

            // Draw table rows
            let yPos = tableTop + 5;
            tableData.forEach(row => {
                // Adjust row height for multi-line descriptions
                const descLines = row[0].split('\n');
                const lineCount = Math.min(descLines.length, 6);
                const rowHeight = Math.max(10, lineCount * 5 + 5);

                // Handle multi-line description
                let lineHeight = 0;

                // Display first line
                doc.text(descLines[0].substring(0, 30), 22, yPos + lineHeight);

                // Display additional lines if any
                for (let i = 1; i < descLines.length && i < 6; i++) {
                    lineHeight += 5;
                    doc.text(descLines[i].substring(0, 30), 22, yPos + lineHeight);
                }
                doc.text(row[1], 80, yPos);
                doc.text(row[2], 100, yPos);
                doc.text(row[3], 130, yPos);
                doc.text(row[4], 160, yPos);

                yPos += rowHeight;

                // Draw horizontal line after each row
                doc.line(20, yPos, 190, yPos);
            });

            // Summary
            const summaryX = 130;
            let summaryY = yPos + 20;

            // Get the summary spacing and alignment from settings or use defaults
            const settings = JSON.parse(localStorage.getItem('invoice-maker-settings') || '{}');
            const summarySpacing = settings.summarySpacing || 40; // Default to 40mm if not set
            const summaryAlignment = settings.summaryAlignment || 'right'; // Default to right alignment

            // Calculate the position for the amount based on the user's spacing preference
            const amountX = summaryX + summarySpacing;

            doc.setFontSize(9);
            doc.setTextColor(0, 0, 0);

            // Format all amounts to ensure consistent decimal places
            const subtotalText = `${data.subtotal.toFixed(2)}`;
            const taxText = `${data.taxTotal.toFixed(2)}`;
            const totalText = `Total ${data.currency}:`;
            const grandTotalText = `${data.grandTotal.toFixed(2)}`;

            // Subtotal row
            doc.text('Subtotal:', summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(subtotalText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(subtotalText, amountX, summaryY);
            }

            summaryY += 7;

            // Tax row
            doc.text('Tax:', summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(taxText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(taxText, amountX, summaryY);
            }

            summaryY += 7;
            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);

            // Total row
            doc.text(totalText, summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(grandTotalText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(grandTotalText, amountX, summaryY);
            }

            // Notes
            if (data.notes) {
                summaryY += 20;
                doc.setFontSize(10);
                doc.setTextColor(0, 0, 0);
                doc.text(`${data.notesTitle || 'Notes'}:`, 20, summaryY);

                summaryY += 7;
                doc.setFontSize(9);
                doc.setTextColor(100, 100, 100);
                const notesLines = data.notes.split(/\r?\n/);
                let noteY = summaryY;

                notesLines.forEach(line => {
                    doc.text(line, 20, noteY);
                    noteY += 5;
                });
            }

            // Footer
            doc.setFontSize(9);
            doc.setTextColor(100, 100, 100);
            doc.text('Thank you for your business!', 105, 280, { align: 'center' });
        }
    },

    // Creative Template
    creative: {
        name: 'Creative',
        description: 'A bold, colorful design for creative businesses.',

        // Generate HTML for preview
        generateHtml: function(data) {
            return `
                <div class="invoice-preview creative-template">
                    <div class="invoice-header">
                        <div class="invoice-title">
                            <h1>INVOICE</h1>
                            <div class="invoice-number">#${data.invoiceNumber || 'New Invoice'}</div>
                        </div>
                        <div class="invoice-company">
                            <h2>${data.business.name || 'Your Business'}</h2>
                            <p>${data.business.address || ''}</p>
                            <p>${data.business.phone || ''}</p>
                            <p>${data.business.email || ''}</p>
                            <p>${data.business.website || ''}</p>
                        </div>
                    </div>

                    <div class="invoice-info">
                        <div class="client-info">
                            <h3>Bill To:</h3>
                            <h4>${data.client.name || 'Client Name'}</h4>
                            <p>${data.client.address || ''}</p>
                            <p>${data.client.phone || ''}</p>
                            <p>${data.client.email || ''}</p>
                        </div>
                        <div class="invoice-details">
                            <div class="detail-row">
                                <span>Invoice Date:</span>
                                <span>${data.invoiceDate || ''}</span>
                            </div>
                            <div class="detail-row">
                                <span>Due Date:</span>
                                <span>${data.dueDate || ''}</span>
                            </div>
                        </div>
                    </div>

                    <div class="invoice-items">
                        <table class="creative-table">
                            <thead>
                                <tr>
                                    <th>Description</th>
                                    <th>Quantity</th>
                                    <th>Unit Price</th>
                                    <th>Tax (%)</th>
                                    <th>Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${data.items.map(item => `
                                    <tr>
                                        <td>${item.description || 'Item'}</td>
                                        <td>${item.quantity}</td>
                                        <td>${data.currencySymbol}${item.price.toFixed(2)}</td>
                                        <td>${item.taxRate}%</td>
                                        <td>${data.currencySymbol}${item.amount.toFixed(2)}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div class="invoice-summary">
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span>${data.currencySymbol}${data.subtotal.toFixed(2)}</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax:</span>
                            <span>${data.currencySymbol}${data.taxTotal.toFixed(2)}</span>
                        </div>
                        <div class="summary-row total">
                            <span>Total:</span>
                            <span>${data.currencySymbol}${data.grandTotal.toFixed(2)}</span>
                        </div>
                    </div>

                    ${data.notes ? `
                        <div class="invoice-notes">
                            <h3>${data.notesTitle || 'Notes'}</h3>
                            <p>${data.notes}</p>
                        </div>
                    ` : ''}

                    <div class="invoice-footer">
                        <p>Thanks for your business! We appreciate you!</p>
                    </div>
                </div>
            `;
        },

        // Generate PDF
        generatePdf: function(doc, data) {
            // Set font
            doc.setFont('helvetica');

            // Add a colored header bar
            doc.setFillColor(155, 89, 182); // Purple color
            doc.rect(0, 0, doc.internal.pageSize.width, 50, 'F');

            // Header
            doc.setFontSize(32);
            doc.setTextColor(255, 255, 255); // White color
            doc.text('INVOICE', 20, 30);

            doc.setFontSize(12);
            doc.text(`#${data.invoiceNumber || 'New Invoice'}`, 20, 40);

            // Business info
            doc.setFontSize(16);
            doc.text(data.business.name || 'Your Business', 120, 25);

            doc.setFontSize(10);

            // Handle business info with dynamic positioning to avoid gaps
            let businessInfoY = 32;
            const lineSpacing = 5;

            if (data.business.address && data.business.address.trim()) {
                doc.text(data.business.address.split('\n'), 120, businessInfoY);
                businessInfoY += data.business.address.split('\n').length * lineSpacing;
            }

            if (data.business.phone && data.business.phone.trim()) {
                doc.text(data.business.phone, 120, businessInfoY);
                businessInfoY += lineSpacing;
            }

            if (data.business.email && data.business.email.trim()) {
                doc.text(data.business.email, 120, businessInfoY);
                businessInfoY += lineSpacing;
            }

            if (data.business.website && data.business.website.trim()) {
                doc.text(data.business.website, 120, businessInfoY);
            }

            // Client info
            doc.setFontSize(12);
            doc.setTextColor(155, 89, 182); // Purple color
            doc.text('Bill To:', 20, 70);

            doc.setFontSize(11);
            doc.setTextColor(0, 0, 0);
            doc.text(data.client.name || 'Client Name', 20, 77);

            doc.setFontSize(10);
            doc.setTextColor(100, 100, 100);

            // Handle client info with dynamic positioning to avoid gaps
            let clientInfoY = 84;

            if (data.client.address && data.client.address.trim()) {
                doc.text(data.client.address.split('\n'), 20, clientInfoY);
                clientInfoY += data.client.address.split('\n').length * lineSpacing;
            }

            if (data.client.phone && data.client.phone.trim()) {
                doc.text(data.client.phone, 20, clientInfoY);
                clientInfoY += lineSpacing;
            }

            if (data.client.email && data.client.email.trim()) {
                doc.text(data.client.email, 20, clientInfoY);
            }

            // Invoice details
            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);
            doc.text('Invoice Date:', 120, 77);
            doc.text(data.invoiceDate || '', 160, 77);

            doc.text('Due Date:', 120, 84);
            doc.text(data.dueDate || '', 160, 84);

            // Items table
            const tableTop = 110;
            const tableHeaders = ['Description', 'Qty', 'Unit Price', 'Tax (%)', 'Amount'];
            const tableData = data.items.map(item => [
                item.description || 'Item',
                item.quantity.toString(),
                `${item.price.toFixed(2)}`,
                `${item.taxRate}%`,
                `${item.amount.toFixed(2)}`
            ]);

            // Draw table headers
            doc.setFillColor(155, 89, 182); // Purple color
            doc.rect(20, tableTop - 10, 170, 10, 'F');

            doc.setFontSize(10);
            doc.setTextColor(255, 255, 255); // White text
            doc.text(tableHeaders[0], 22, tableTop - 3);
            doc.text(tableHeaders[1], 80, tableTop - 3);
            doc.text(tableHeaders[2], 100, tableTop - 3);
            doc.text(tableHeaders[3], 130, tableTop - 3);
            doc.text(tableHeaders[4], 160, tableTop - 3);

            // Draw table rows
            let yPos = tableTop;
            tableData.forEach((row, index) => {
                // Adjust row height for multi-line descriptions
                const descLines = row[0].split('\n');
                const lineCount = Math.min(descLines.length, 6);
                const rowHeight = Math.max(10, lineCount * 5 + 5);

                // Alternate row background
                if (index % 2 === 1) {
                    doc.setFillColor(243, 229, 245); // Light purple
                    doc.rect(20, yPos, 170, rowHeight, 'F');
                }

                // Handle multi-line description
                let lineHeight = 5;

                // Display first line
                doc.setTextColor(0, 0, 0);
                doc.text(descLines[0].substring(0, 30), 22, yPos + lineHeight);

                // Display additional lines if any
                for (let i = 1; i < descLines.length && i < 6; i++) {
                    lineHeight += 5;
                    doc.text(descLines[i].substring(0, 30), 22, yPos + lineHeight);
                }
                doc.text(row[1], 80, yPos + 5);
                doc.text(row[2], 100, yPos + 5);
                doc.text(row[3], 130, yPos + 5);
                doc.text(row[4], 160, yPos + 5);

                yPos += rowHeight;
            });

            // Draw table border
            doc.setDrawColor(206, 147, 216); // Light purple
            doc.rect(20, tableTop - 10, 170, yPos - tableTop + 10, 'S');

            // Summary
            const summaryX = 130;
            let summaryY = yPos + 20;

            // Get the summary spacing and alignment from settings or use defaults
            const settings = JSON.parse(localStorage.getItem('invoice-maker-settings') || '{}');
            const summarySpacing = settings.summarySpacing || 40; // Default to 40mm if not set
            const summaryAlignment = settings.summaryAlignment || 'right'; // Default to right alignment

            // Calculate the position for the amount based on the user's spacing preference
            const amountX = summaryX + summarySpacing;

            doc.setFontSize(10);
            doc.setTextColor(0, 0, 0);

            // Format all amounts to ensure consistent decimal places
            const subtotalText = `${data.subtotal.toFixed(2)}`;
            const taxText = `${data.taxTotal.toFixed(2)}`;
            const totalText = `Total ${data.currency}:`;
            const grandTotalText = `${data.grandTotal.toFixed(2)}`;

            // Subtotal row
            doc.text('Subtotal:', summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(subtotalText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(subtotalText, amountX, summaryY);
            }

            summaryY += 7;

            // Tax row
            doc.text('Tax:', summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(taxText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(taxText, amountX, summaryY);
            }

            summaryY += 7;
            doc.setFontSize(12);
            doc.setTextColor(155, 89, 182); // Purple color

            // Total row
            doc.text(totalText, summaryX, summaryY);
            if (summaryAlignment === 'right') {
                doc.text(grandTotalText, amountX, summaryY, { align: 'right' });
            } else {
                doc.text(grandTotalText, amountX, summaryY);
            }

            // Notes
            if (data.notes) {
                summaryY += 20;
                doc.setFontSize(11);
                doc.setTextColor(155, 89, 182); // Purple color
                doc.text(`${data.notesTitle || 'Notes'}:`, 20, summaryY);

                summaryY += 7;
                doc.setFontSize(10);
                doc.setTextColor(100, 100, 100);
                const notesLines = data.notes.split(/\r?\n/);
                let noteY = summaryY;

                notesLines.forEach(line => {
                    doc.text(line, 20, noteY);
                    noteY += 5;
                });
            }

            // Footer
            doc.setFontSize(10);
            doc.setTextColor(155, 89, 182); // Purple color
            doc.text('Thanks for your business! We appreciate you!', 105, 280, { align: 'center' });
        }
    }
};
