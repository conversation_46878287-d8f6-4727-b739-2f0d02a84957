document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebar = document.querySelector('.sidebar');
    const mainContent = document.querySelector('.main-content');
    const navItems = document.querySelectorAll('.sidebar-nav li');
    const pageTitle = document.getElementById('page-title');
    const pages = document.querySelectorAll('.page-content');
    const toolCards = document.querySelectorAll('.tool-card');
    const toolLaunchButtons = document.querySelectorAll('.tool-launch-btn');
    const themeSelect = document.getElementById('theme-select');
    let isMobile = window.innerWidth <= 768;

    // Create an overlay element
    const overlay = document.createElement('div');
    overlay.classList.add('content-overlay');
    mainContent.appendChild(overlay);

    // Toggle sidebar collapse
    sidebarToggle.addEventListener('click', function() {
        sidebar.classList.toggle('collapsed');
        if (isMobile) {
            overlay.classList.toggle('active', sidebar.classList.contains('collapsed'));
        }
    });

    // Close sidebar when overlay is clicked
    overlay.addEventListener('click', function() {
        if (isMobile && sidebar.classList.contains('collapsed')) {
            sidebar.classList.remove('collapsed');
            overlay.classList.remove('active');
        }
    });

    // Navigation handling
    navItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();

            // Update active state
            navItems.forEach(nav => nav.classList.remove('active'));
            this.classList.add('active');

            // Get the page to show
            const pageId = this.getAttribute('data-page');

            // Update page title
            updatePageTitle(pageId);

            // Show the selected page
            showPage(pageId);

            // Close sidebar on mobile after selection
            if (isMobile && sidebar.classList.contains('collapsed')) {
                sidebar.classList.remove('collapsed');
                overlay.classList.remove('active');
            }
        });
    });

    // Tool card click handling
    toolCards.forEach(card => {
        card.addEventListener('click', function() {
            const toolId = this.getAttribute('data-tool');
            navigateToTool(toolId);
        });
    });

    // Tool launch button click handling
    toolLaunchButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation(); // Prevent the card click event
            const toolId = this.closest('.tool-card').getAttribute('data-tool');
            navigateToTool(toolId);
        });
    });

    // Theme handling
    themeSelect.addEventListener('change', function() {
        setTheme(this.value);
    });

    // Load saved theme
    loadSavedTheme();

    // Functions
    function showPage(pageId) {
        pages.forEach(page => {
            page.classList.remove('active');
        });

        const targetPage = document.getElementById(`${pageId}-page`);
        if (targetPage) {
            targetPage.classList.add('active');
        }
    }

    function updatePageTitle(pageId) {
        switch(pageId) {
            case 'home':
                pageTitle.textContent = 'Dashboard';
                break;
            case 'csv-ofx':
                pageTitle.textContent = 'CSV to OFX Converter';
                break;
            case 'time-calculator':
                pageTitle.textContent = 'Payroll Time Calculator';
                break;
            case 'invoice-maker':
                pageTitle.textContent = 'Invoice Maker';
                break;
            case 'calculator':
                pageTitle.textContent = 'Advanced Calculator';
                break;
            default:
                pageTitle.textContent = 'Dashboard';
        }
    }

    function navigateToTool(toolId) {
        // Find the nav item
        const navItem = document.querySelector(`.sidebar-nav li[data-page="${toolId}"]`);
        if (navItem) {
            // Simulate a click on the nav item
            navItem.click();
        }
    }

    function loadSavedTheme() {
        const savedTheme = localStorage.getItem('dashboard-theme');
        if (savedTheme) {
            setTheme(savedTheme);
            themeSelect.value = savedTheme;
        }
    }

    function setTheme(themeName) {
        if (themeName === 'default') {
            document.body.removeAttribute('data-theme');
        } else {
            document.body.setAttribute('data-theme', themeName);
        }
        localStorage.setItem('dashboard-theme', themeName);

        // Also update theme in iframes if they're loaded
        updateIframeThemes(themeName);
    }

    function updateIframeThemes(themeName) {
        // CSV-OFX iframe
        const csvOfxFrame = document.getElementById('csv-ofx-frame');
        if (csvOfxFrame && csvOfxFrame.contentWindow) {
            try {
                // Use postMessage to communicate with the iframe
                csvOfxFrame.contentWindow.postMessage({
                    action: 'setTheme',
                    theme: themeName
                }, '*');
            } catch (e) {
                console.log('Could not update CSV-OFX iframe theme:', e);
            }
        }

        // Time Calculator iframe
        const timeCalcFrame = document.getElementById('time-calculator-frame');
        if (timeCalcFrame && timeCalcFrame.contentWindow) {
            try {
                // Use postMessage for this iframe too
                timeCalcFrame.contentWindow.postMessage({
                    action: 'setTheme',
                    theme: themeName
                }, '*');
            } catch (e) {
                console.log('Could not update Time Calculator iframe theme:', e);
            }
        }

        // Invoice Maker iframe
        const invoiceMakerFrame = document.getElementById('invoice-maker-frame');
        if (invoiceMakerFrame && invoiceMakerFrame.contentWindow) {
            try {
                // Use postMessage for this iframe too
                invoiceMakerFrame.contentWindow.postMessage({
                    action: 'setTheme',
                    theme: themeName
                }, '*');
            } catch (e) {
                console.log('Could not update Invoice Maker iframe theme:', e);
            }
        }

        // Calculator iframe
        const calculatorFrame = document.getElementById('calculator-frame');
        if (calculatorFrame && calculatorFrame.contentWindow) {
            try {
                // Use postMessage for this iframe too
                calculatorFrame.contentWindow.postMessage({
                    action: 'setTheme',
                    theme: themeName
                }, '*');
            } catch (e) {
                console.log('Could not update Calculator iframe theme:', e);
            }
        }
    }

    // Handle iframe load events to sync themes
    document.getElementById('csv-ofx-frame').addEventListener('load', function() {
        const currentTheme = localStorage.getItem('dashboard-theme') || 'default';
        updateIframeThemes(currentTheme);
    });

    document.getElementById('time-calculator-frame').addEventListener('load', function() {
        const currentTheme = localStorage.getItem('dashboard-theme') || 'default';
        updateIframeThemes(currentTheme);
    });

    document.getElementById('invoice-maker-frame').addEventListener('load', function() {
        const currentTheme = localStorage.getItem('dashboard-theme') || 'default';
        updateIframeThemes(currentTheme);
    });

    document.getElementById('calculator-frame').addEventListener('load', function() {
        const currentTheme = localStorage.getItem('dashboard-theme') || 'default';
        updateIframeThemes(currentTheme);
    });
});
