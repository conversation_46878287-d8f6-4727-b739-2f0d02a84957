/* Calculator Theme Variables */
:root {
    /* Light Blue Theme (Default) */
    --primary-color: #3498db;       /* Main blue */
    --primary-dark: #2980b9;        /* Darker blue */
    --secondary-color: #2ecc71;     /* Green */
    --secondary-dark: #27ae60;      /* Darker green */
    --accent-color: #e74c3c;        /* Red accent */
    --accent-dark: #c0392b;         /* Darker red */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --bg-color: #ffffff;            /* Background color */
    --text-color: #333333;          /* Text color */
    --heading-color: #2c3e50;       /* Heading color */

    /* Calculator Specific */
    --display-bg: #f1f8fe;          /* Display background */
    --display-text: #2c3e50;        /* Display text */
    --number-btn-bg: #f8f9fa;       /* Number button background */
    --number-btn-text: #333333;     /* Number button text */
    --operator-btn-bg: #e1f0fa;     /* Operator button background */
    --operator-btn-text: #2980b9;   /* Operator button text */
    --function-btn-bg: #eaf7ef;     /* Function button background */
    --function-btn-text: #27ae60;   /* Function button text */
    --equals-btn-bg: #3498db;       /* Equals button background */
    --equals-btn-text: #ffffff;     /* Equals button text */
    --clear-btn-bg: #fde9e8;        /* Clear button background */
    --clear-btn-text: #e74c3c;      /* Clear button text */
    --memory-indicator: #e74c3c;    /* Memory indicator */
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #4dabf7;       /* Brighter blue for dark mode */
    --primary-dark: #339af0;        /* Slightly lighter blue */
    --secondary-color: #51cf66;     /* Brighter green */
    --secondary-dark: #40c057;      /* Slightly lighter green */
    --accent-color: #ff6b6b;        /* Brighter red */
    --accent-dark: #fa5252;         /* Slightly lighter red */
    --neutral-light: #343a40;       /* Dark background */
    --neutral-medium: #495057;      /* Medium dark background */
    --neutral-dark: #dee2e6;        /* Light text */
    --shadow: rgba(0, 0, 0, 0.3);   /* Darker shadow */
    --bg-color: #212529;            /* Dark background */
    --text-color: #f8f9fa;          /* Light text */
    --heading-color: #f8f9fa;       /* Heading color */

    /* Calculator Specific */
    --display-bg: #2c3e50;          /* Display background */
    --display-text: #ecf0f1;        /* Display text */
    --number-btn-bg: #343a40;       /* Number button background */
    --number-btn-text: #f8f9fa;     /* Number button text */
    --operator-btn-bg: #264b73;     /* Operator button background */
    --operator-btn-text: #4dabf7;   /* Operator button text */
    --function-btn-bg: #2a4d3e;     /* Function button background */
    --function-btn-text: #51cf66;   /* Function button text */
    --equals-btn-bg: #4dabf7;       /* Equals button background */
    --equals-btn-text: #ffffff;     /* Equals button text */
    --clear-btn-bg: #5a2e2b;        /* Clear button background */
    --clear-btn-text: #ff6b6b;      /* Clear button text */
    --memory-indicator: #ff6b6b;    /* Memory indicator */
}

/* Purple Theme */
[data-theme="purple"] {
    --primary-color: #9775fa;       /* Main purple */
    --primary-dark: #845ef7;        /* Darker purple */
    --secondary-color: #5c7cfa;     /* Blue secondary */
    --secondary-dark: #4c6ef5;      /* Darker blue */
    --accent-color: #ff8787;        /* Coral accent */
    --accent-dark: #ff6b6b;         /* Darker coral */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --bg-color: #ffffff;            /* Background color */
    --text-color: #333333;          /* Text color */
    --heading-color: #5f3dc4;       /* Heading color */

    /* Calculator Specific */
    --display-bg: #f3f0ff;          /* Display background */
    --display-text: #5f3dc4;        /* Display text */
    --number-btn-bg: #f8f9fa;       /* Number button background */
    --number-btn-text: #333333;     /* Number button text */
    --operator-btn-bg: #e5dbff;     /* Operator button background */
    --operator-btn-text: #845ef7;   /* Operator button text */
    --function-btn-bg: #dbe4ff;     /* Function button background */
    --function-btn-text: #4c6ef5;   /* Function button text */
    --equals-btn-bg: #9775fa;       /* Equals button background */
    --equals-btn-text: #ffffff;     /* Equals button text */
    --clear-btn-bg: #fff0f6;        /* Clear button background */
    --clear-btn-text: #ff6b6b;      /* Clear button text */
    --memory-indicator: #ff8787;    /* Memory indicator */
}

/* Green Theme */
[data-theme="green"] {
    --primary-color: #40c057;       /* Main green */
    --primary-dark: #37b24d;        /* Darker green */
    --secondary-color: #4dabf7;     /* Blue secondary */
    --secondary-dark: #339af0;      /* Darker blue */
    --accent-color: #ff922b;        /* Orange accent */
    --accent-dark: #fd7e14;         /* Darker orange */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --bg-color: #ffffff;            /* Background color */
    --text-color: #333333;          /* Text color */
    --heading-color: #2b8a3e;       /* Heading color */

    /* Calculator Specific */
    --display-bg: #ebfbee;          /* Display background */
    --display-text: #2b8a3e;        /* Display text */
    --number-btn-bg: #f8f9fa;       /* Number button background */
    --number-btn-text: #333333;     /* Number button text */
    --operator-btn-bg: #d3f9d8;     /* Operator button background */
    --operator-btn-text: #37b24d;   /* Operator button text */
    --function-btn-bg: #d0ebff;     /* Function button background */
    --function-btn-text: #339af0;   /* Function button text */
    --equals-btn-bg: #40c057;       /* Equals button background */
    --equals-btn-text: #ffffff;     /* Equals button text */
    --clear-btn-bg: #fff4e6;        /* Clear button background */
    --clear-btn-text: #fd7e14;      /* Clear button text */
    --memory-indicator: #ff922b;    /* Memory indicator */
}

/* Dark Blue Theme */
[data-theme="dark-blue"] {
    --primary-color: #228be6;       /* Main blue */
    --primary-dark: #1c7ed6;        /* Darker blue */
    --secondary-color: #15aabf;     /* Teal secondary */
    --secondary-dark: #1098ad;      /* Darker teal */
    --accent-color: #f76707;        /* Orange accent */
    --accent-dark: #e8590c;         /* Darker orange */
    --neutral-light: #343a40;       /* Dark background */
    --neutral-medium: #495057;      /* Medium dark background */
    --neutral-dark: #dee2e6;        /* Light text */
    --shadow: rgba(0, 0, 0, 0.3);   /* Darker shadow */
    --bg-color: #1e2a3a;            /* Dark blue background */
    --text-color: #f8f9fa;          /* Light text */
    --heading-color: #f8f9fa;       /* Heading color */

    /* Calculator Specific */
    --display-bg: #1a3a5f;          /* Display background */
    --display-text: #e7f5ff;        /* Display text */
    --number-btn-bg: #263a4e;       /* Number button background */
    --number-btn-text: #f8f9fa;     /* Number button text */
    --operator-btn-bg: #1864ab;     /* Operator button background */
    --operator-btn-text: #74c0fc;   /* Operator button text */
    --function-btn-bg: #0b7285;     /* Function button background */
    --function-btn-text: #66d9e8;   /* Function button text */
    --equals-btn-bg: #228be6;       /* Equals button background */
    --equals-btn-text: #ffffff;     /* Equals button text */
    --clear-btn-bg: #862e1c;        /* Clear button background */
    --clear-btn-text: #ffc078;      /* Clear button text */
    --memory-indicator: #ffc078;    /* Memory indicator */
}

/* Basic Reset & Font */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
    padding: 40px 20px;
    margin: 0;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

/* Calculator Container Styling */
.calculator-container {
    background-color: var(--bg-color);
    padding: 30px 40px;
    border-radius: 12px;
    box-shadow: 0 8px 24px var(--shadow);
    text-align: center;
    max-width: 600px;
    width: 100%;
    overflow: hidden;
}

h1 {
    color: var(--heading-color);
    margin-bottom: 25px;
    font-weight: 700;
    font-size: 1.8em;
    text-align: center;
}

/* Calculator Styling */
.calculator {
    background-color: var(--bg-color);
    border-radius: 10px;
    overflow: hidden;
}

/* Display Styling */
.display-container {
    margin-bottom: 20px;
}

.history {
    max-height: 150px;
    overflow-y: auto;
    text-align: right;
    padding: 10px;
    font-size: 0.9em;
    color: var(--text-color);
    opacity: 0.7;
    border-radius: 8px 8px 0 0;
    background-color: var(--neutral-light);
    margin-bottom: 5px;
    display: none; /* Hidden by default, shown when toggled */
}

.history.show {
    display: block;
}

.history-item {
    margin-bottom: 5px;
    padding: 5px 0;
    border-bottom: 1px solid var(--neutral-medium);
}

.display {
    background-color: var(--display-bg);
    padding: 20px;
    border-radius: 8px;
    text-align: right;
    box-shadow: inset 0 2px 5px var(--shadow);
}

.expression {
    font-size: 1.2em;
    min-height: 1.5em;
    margin-bottom: 10px;
    color: var(--display-text);
    opacity: 0.7;
    word-break: break-all;
    border-bottom: 1px solid var(--neutral-medium);
    padding-bottom: 8px;
}

.result {
    font-size: 2.8em;
    font-weight: 700;
    color: var(--display-text);
    word-break: break-all;
    padding-top: 5px;
}

/* Buttons Container */
.buttons-container {
    display: grid;
    gap: 10px;
}

.button-row {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
}

/* Button Styling */
.btn {
    padding: 15px 10px;
    border: none;
    border-radius: 8px;
    font-size: 1.1em;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px var(--shadow);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px var(--shadow);
}

.btn:active {
    transform: translateY(1px);
    box-shadow: 0 1px 2px var(--shadow);
}

/* Number Buttons */
.number-btn {
    background-color: var(--number-btn-bg);
    color: var(--number-btn-text);
}

/* Operator Buttons */
.operator-btn {
    background-color: var(--operator-btn-bg);
    color: var(--operator-btn-text);
    font-weight: 700;
}

/* Function Buttons */
.function-btn {
    background-color: var(--function-btn-bg);
    color: var(--function-btn-text);
}

/* Equals Button */
.equals-btn {
    background-color: var(--equals-btn-bg);
    color: var(--equals-btn-text);
    font-weight: 700;
}

/* Clear Buttons */
.clear-btn {
    background-color: var(--clear-btn-bg);
    color: var(--clear-btn-text);
    font-weight: 700;
}

/* Memory Indicator */
.memory-active {
    position: relative;
}

.memory-active::after {
    content: "M";
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 0.7em;
    color: var(--memory-indicator);
    font-weight: bold;
}

/* Responsive Adjustments */
@media (max-width: 600px) {
    .calculator-container {
        padding: 20px;
    }

    .button-row {
        gap: 5px;
    }

    .btn {
        padding: 12px 5px;
        font-size: 0.9em;
    }

    .result {
        font-size: 2em;
    }
}

/* Animation for button press */
@keyframes buttonPress {
    0% { transform: scale(1); }
    50% { transform: scale(0.95); }
    100% { transform: scale(1); }
}

.btn-pressed {
    animation: buttonPress 0.2s ease;
}
