document.addEventListener('DOMContentLoaded', function() {
    // Template Customizer Variables
    let customTemplate = {};
    let defaultTemplate = {
        name: 'Custom Template',
        general: {
            pageSize: 'a4',
            orientation: 'portrait',
            margins: { top: 20, right: 20, bottom: 20, left: 20 }
        },
        header: {
            height: 50,
            backgroundColor: '#ffffff',
            title: 'INVOICE',
            titleColor: '#3498db',
            titleSize: 24,
            companyInfoAlign: 'right',
            companyNameSize: 14
        },
        table: {
            style: 'classic',
            headerBackground: '#f8f9fa',
            headerTextColor: '#000000',
            alternatingRows: true,
            alternateRowColor: '#f2f2f2',
            borderColor: '#dee2e6',
            borderWidth: 1,
            columns: {
                item: true,
                quantity: true,
                price: true,
                tax: true,
                amount: true
            }
        },
        footer: {
            text: 'Thank you for your business!',
            textColor: '#6c757d',
            fontSize: 10,
            showPageNumbers: true
        },
        fonts: {
            primary: 'helvetica',
            sizeNormal: 10,
            sizeSmall: 8
        },
        colors: {
            primary: '#3498db',
            secondary: '#2ecc71',
            accent: '#e74c3c',
            text: '#333333',
            background: '#ffffff'
        }
    };

    // DOM Elements
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabPanes = document.querySelectorAll('.tab-pane');
    // Theme is now managed by the parent dashboard
    const addItemBtn = document.getElementById('add-item-btn');
    const invoiceItemsBody = document.getElementById('invoice-items-body');
    const previewBtn = document.getElementById('preview-btn');
    const exportPdfBtn = document.getElementById('export-pdf-btn');
    const exportExcelBtn = document.getElementById('export-excel-btn');
    const previewModal = document.getElementById('preview-modal');
    const closeModal = document.querySelector('.close-modal');
    const modalCloseBtn = document.getElementById('modal-close-btn');
    const modalExportPdfBtn = document.getElementById('modal-export-pdf-btn');
    const modalExportExcelBtn = document.getElementById('modal-export-excel-btn');
    const invoicePreviewContainer = document.getElementById('invoice-preview-container');
    const templateCards = document.querySelectorAll('.template-card');
    const saveSettingsBtn = document.getElementById('save-settings-btn');
    const resetSettingsBtn = document.getElementById('reset-settings-btn');
    const statusArea = document.getElementById('status-area');

    // Preset Management Elements
    const presetSelector = document.getElementById('preset-selector');
    const presetNameInput = document.getElementById('preset-name');
    const savePresetBtn = document.getElementById('save-preset-btn');
    const deletePresetBtn = document.getElementById('delete-preset-btn');

    // State variables
    let selectedTemplate = 'classic';
    let itemCounter = 0;
    let settings = loadSettings();

    // Initialize the form with current date
    initializeForm();

    // Initialize template customizer
    initTemplateCustomizer();

    // Initialize preset system
    initializePresets();

    // Load custom templates
    loadCustomTemplates();

    // Initialize summary spacing slider
    initSummarySpacingSlider();

    // Tab switching
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons and panes
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabPanes.forEach(pane => pane.classList.remove('active'));

            // Add active class to clicked button and corresponding pane
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });

    // Listen for theme messages from parent window
    window.addEventListener('message', function(event) {
        if (event.data && event.data.action === 'setTheme') {
            setTheme(event.data.theme);
        }
    });

    // Add invoice item
    addItemBtn.addEventListener('click', addInvoiceItem);

    // Preview invoice
    previewBtn.addEventListener('click', function() {
        generateInvoicePreview();
        previewModal.style.display = 'block';
    });

    // Export as PDF
    exportPdfBtn.addEventListener('click', () => {
        exportToPdf();
        autoSaveCurrentPreset();
    });
    modalExportPdfBtn.addEventListener('click', () => {
        exportToPdf();
        autoSaveCurrentPreset();
    });

    // Export as Excel
    exportExcelBtn.addEventListener('click', () => {
        exportToExcel();
        autoSaveCurrentPreset();
    });
    modalExportExcelBtn.addEventListener('click', () => {
        exportToExcel();
        autoSaveCurrentPreset();
    });

    // Preset Management Event Listeners
    presetSelector.addEventListener('change', loadSelectedPreset);
    savePresetBtn.addEventListener('click', saveCurrentPreset);
    deletePresetBtn.addEventListener('click', deleteSelectedPreset);
    presetNameInput.addEventListener('input', updateSaveButtonState);

    // Close modal
    closeModal.addEventListener('click', function() {
        previewModal.style.display = 'none';
    });

    modalCloseBtn.addEventListener('click', function() {
        previewModal.style.display = 'none';
    });

    // Close modal when clicking outside
    window.addEventListener('click', function(event) {
        if (event.target === previewModal) {
            previewModal.style.display = 'none';
        }
    });

    // Template selection
    templateCards.forEach(card => {
        card.addEventListener('click', function() {
            templateCards.forEach(c => c.classList.remove('active'));
            this.classList.add('active');
            selectedTemplate = this.getAttribute('data-template');
            setStatus(`Template "${selectedTemplate}" selected.`, 'success');

            // Save selected template to settings
            settings.selectedTemplate = selectedTemplate;
            saveSettings(settings);
        });
    });

    // Save settings
    saveSettingsBtn.addEventListener('click', function() {
        settings.defaultCurrency = document.getElementById('default-currency').value;
        settings.defaultTaxRate = document.getElementById('default-tax-rate').value;
        settings.pdfPageSize = document.getElementById('pdf-page-size').value;
        settings.pdfOrientation = document.getElementById('pdf-orientation').value;
        settings.summarySpacing = parseInt(document.getElementById('summary-spacing').value);
        settings.summaryAlignment = document.getElementById('summary-alignment').value;

        saveSettings(settings);
        setStatus('Settings saved successfully.', 'success');
    });

    // Reset settings
    resetSettingsBtn.addEventListener('click', function() {
        settings = getDefaultSettings();
        saveSettings(settings);
        loadSettingsIntoForm();
        setStatus('Settings reset to defaults.', 'info');
    });

    // Functions
    function initializeForm() {
        // Set current date for invoice date
        const today = new Date();
        const formattedDate = today.toISOString().split('T')[0]; // YYYY-MM-DD
        document.getElementById('invoice-date').value = formattedDate;

        // Set due date (today + 30 days)
        const dueDate = new Date();
        dueDate.setDate(today.getDate() + 30);
        const formattedDueDate = dueDate.toISOString().split('T')[0];
        document.getElementById('due-date').value = formattedDueDate;

        // Add first empty item
        addInvoiceItem();

        // Load settings into form
        loadSettingsIntoForm();

        // Set selected template
        if (settings.selectedTemplate) {
            selectedTemplate = settings.selectedTemplate;
            const templateCard = document.querySelector(`.template-card[data-template="${selectedTemplate}"]`);
            if (templateCard) {
                templateCards.forEach(c => c.classList.remove('active'));
                templateCard.classList.add('active');
            }
        }
    }

    function loadSettingsIntoForm() {
        document.getElementById('default-currency').value = settings.defaultCurrency;
        document.getElementById('default-tax-rate').value = settings.defaultTaxRate;
        document.getElementById('pdf-page-size').value = settings.pdfPageSize;
        document.getElementById('pdf-orientation').value = settings.pdfOrientation;

        // Set summary spacing slider
        const summarySpacingSlider = document.getElementById('summary-spacing');
        if (summarySpacingSlider) {
            summarySpacingSlider.value = settings.summarySpacing || 40;
            // Update the displayed value
            const sliderValue = summarySpacingSlider.nextElementSibling;
            if (sliderValue) {
                sliderValue.textContent = summarySpacingSlider.value;
            }
        }

        // Set summary alignment
        const summaryAlignmentSelect = document.getElementById('summary-alignment');
        if (summaryAlignmentSelect) {
            summaryAlignmentSelect.value = settings.summaryAlignment || 'right';
        }
    }

    // Template Customizer Functions
    function initTemplateCustomizer() {
        // Load saved template or use default
        customTemplate = loadCustomTemplate() || JSON.parse(JSON.stringify(defaultTemplate));

        // Initialize form values from template
        loadTemplateIntoForm();

        // Setup section navigation
        setupCustomizerNavigation();

        // Setup form event listeners
        setupCustomizerEvents();

        // Generate initial preview
        updateTemplatePreview();
    }

    function loadCustomTemplate() {
        console.log('Attempting to load custom template from localStorage');
        const savedTemplate = localStorage.getItem('invoice-maker-custom-template');

        if (savedTemplate) {
            console.log('Found saved template in localStorage');
            try {
                const parsedTemplate = JSON.parse(savedTemplate);
                console.log('Successfully parsed template:', parsedTemplate);
                return parsedTemplate;
            } catch (e) {
                console.error('Error parsing saved template:', e);
                return null;
            }
        } else {
            console.log('No saved template found in localStorage');
            return null;
        }
    }

    function saveCustomTemplate(template) {
        try {
            // Log the template being saved for debugging
            console.log('Saving template:', template);

            // Stringify the template and save it to localStorage
            const templateString = JSON.stringify(template);
            localStorage.setItem('invoice-maker-custom-template', templateString);

            // Verify the template was saved correctly
            const savedTemplate = localStorage.getItem('invoice-maker-custom-template');
            console.log('Saved template size:', savedTemplate.length, 'bytes');

            return true;
        } catch (e) {
            console.error('Error saving template:', e);
            return false;
        }
    }

    function loadTemplateIntoForm() {
        // General section
        document.getElementById('template-name').value = customTemplate.name || '';
        document.getElementById('page-size').value = customTemplate.general.pageSize;
        document.getElementById('page-orientation').value = customTemplate.general.orientation;
        document.getElementById('margin-top').value = customTemplate.general.margins.top;
        document.getElementById('margin-right').value = customTemplate.general.margins.right;
        document.getElementById('margin-bottom').value = customTemplate.general.margins.bottom;
        document.getElementById('margin-left').value = customTemplate.general.margins.left;

        // Header section
        document.getElementById('header-height').value = customTemplate.header.height;
        document.getElementById('header-bg-color').value = customTemplate.header.backgroundColor;
        document.getElementById('invoice-title').value = customTemplate.header.title;
        document.getElementById('invoice-title-color').value = customTemplate.header.titleColor;
        document.getElementById('invoice-title-size').value = customTemplate.header.titleSize;
        document.getElementById('company-info-align').value = customTemplate.header.companyInfoAlign;
        document.getElementById('company-name-size').value = customTemplate.header.companyNameSize;

        // Table section
        document.getElementById('table-style').value = customTemplate.table.style;
        document.getElementById('table-header-bg').value = customTemplate.table.headerBackground;
        document.getElementById('table-header-text').value = customTemplate.table.headerTextColor;
        document.getElementById('table-row-alt').checked = customTemplate.table.alternatingRows;
        document.getElementById('table-row-alt-color').value = customTemplate.table.alternateRowColor;
        document.getElementById('table-border-color').value = customTemplate.table.borderColor;
        document.getElementById('table-border-width').value = customTemplate.table.borderWidth;
        document.getElementById('col-item').checked = customTemplate.table.columns.item;
        document.getElementById('col-quantity').checked = customTemplate.table.columns.quantity;
        document.getElementById('col-price').checked = customTemplate.table.columns.price;
        document.getElementById('col-tax').checked = customTemplate.table.columns.tax;
        document.getElementById('col-amount').checked = customTemplate.table.columns.amount;

        // Footer section
        document.getElementById('footer-text').value = customTemplate.footer.text;
        document.getElementById('footer-text-color').value = customTemplate.footer.textColor;
        document.getElementById('footer-font-size').value = customTemplate.footer.fontSize;
        document.getElementById('show-page-numbers').checked = customTemplate.footer.showPageNumbers;

        // Fonts section
        document.getElementById('primary-font').value = customTemplate.fonts.primary;
        document.getElementById('text-size-normal').value = customTemplate.fonts.sizeNormal;
        document.getElementById('text-size-small').value = customTemplate.fonts.sizeSmall;

        // Colors section
        document.getElementById('primary-color').value = customTemplate.colors.primary;
        document.getElementById('secondary-color').value = customTemplate.colors.secondary;
        document.getElementById('accent-color').value = customTemplate.colors.accent;
        document.getElementById('text-color').value = customTemplate.colors.text;
        document.getElementById('background-color').value = customTemplate.colors.background;
    }

    function setupCustomizerNavigation() {
        const navButtons = document.querySelectorAll('.customizer-nav-btn');
        const sections = document.querySelectorAll('.customizer-section');

        navButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all buttons and sections
                navButtons.forEach(btn => btn.classList.remove('active'));
                sections.forEach(section => section.classList.remove('active'));

                // Add active class to clicked button and corresponding section
                this.classList.add('active');
                const sectionId = this.getAttribute('data-section') + '-section';
                document.getElementById(sectionId).classList.add('active');
            });
        });
    }

    function setupCustomizerEvents() {
        // Save template button
        const saveTemplateBtn = document.getElementById('save-template-btn');
        saveTemplateBtn.addEventListener('click', function() {
            updateTemplateFromForm();
            if (saveCustomTemplate(customTemplate)) {
                setStatus('Template saved successfully.', 'success');
                updateTemplatePreview();

                // Reload custom templates to show the newly saved template
                loadCustomTemplates();

                // Navigate to templates tab to show the saved template
                const templatesTabBtn = document.querySelector('.tab-button[data-tab="template-selector"]');
                templatesTabBtn.click();
            } else {
                setStatus('Error saving template.', 'error');
            }
        });

        // Reset template button
        const resetTemplateBtn = document.getElementById('reset-template-btn');
        resetTemplateBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to reset all template changes?')) {
                customTemplate = JSON.parse(JSON.stringify(defaultTemplate));
                loadTemplateIntoForm();
                updateTemplatePreview();
                setStatus('Template reset to defaults.', 'info');
            }
        });

        // Color preset buttons
        const colorPresets = document.querySelectorAll('.color-preset');
        colorPresets.forEach(preset => {
            preset.addEventListener('click', function() {
                const presetName = this.getAttribute('data-preset');
                applyColorPreset(presetName);
                updateTemplatePreview();
            });
        });

        // Add event listeners to all form inputs for real-time preview updates
        const formInputs = document.querySelectorAll('#template-customizer input, #template-customizer select');
        formInputs.forEach(input => {
            // For text, number, and select inputs, listen for both input and change events
            if (input.type === 'text' || input.type === 'number' || input.type === 'color' ||
                input.type === 'date' || input.tagName === 'SELECT' || input.type === 'range') {
                input.addEventListener('input', function() {
                    updateTemplatePreview();
                });
            }

            // For all inputs, also listen for change events (for checkboxes, radios, etc.)
            input.addEventListener('change', function() {
                updateTemplatePreview();
            });
        });
    }

    function updateTemplateFromForm() {
        // Update template object with values from the form
        customTemplate.name = document.getElementById('template-name').value || 'Custom Template';

        // General section
        customTemplate.general.pageSize = document.getElementById('page-size').value;
        customTemplate.general.orientation = document.getElementById('page-orientation').value;
        customTemplate.general.margins.top = parseInt(document.getElementById('margin-top').value);
        customTemplate.general.margins.right = parseInt(document.getElementById('margin-right').value);
        customTemplate.general.margins.bottom = parseInt(document.getElementById('margin-bottom').value);
        customTemplate.general.margins.left = parseInt(document.getElementById('margin-left').value);

        // Header section
        customTemplate.header.height = parseInt(document.getElementById('header-height').value);
        customTemplate.header.backgroundColor = document.getElementById('header-bg-color').value;
        customTemplate.header.title = document.getElementById('invoice-title').value;
        customTemplate.header.titleColor = document.getElementById('invoice-title-color').value;
        customTemplate.header.titleSize = parseInt(document.getElementById('invoice-title-size').value);
        customTemplate.header.companyInfoAlign = document.getElementById('company-info-align').value;
        customTemplate.header.companyNameSize = parseInt(document.getElementById('company-name-size').value);

        // Table section
        customTemplate.table.style = document.getElementById('table-style').value;
        customTemplate.table.headerBackground = document.getElementById('table-header-bg').value;
        customTemplate.table.headerTextColor = document.getElementById('table-header-text').value;
        customTemplate.table.alternatingRows = document.getElementById('table-row-alt').checked;
        customTemplate.table.alternateRowColor = document.getElementById('table-row-alt-color').value;
        customTemplate.table.borderColor = document.getElementById('table-border-color').value;
        customTemplate.table.borderWidth = parseFloat(document.getElementById('table-border-width').value);
        customTemplate.table.columns.item = document.getElementById('col-item').checked;
        customTemplate.table.columns.quantity = document.getElementById('col-quantity').checked;
        customTemplate.table.columns.price = document.getElementById('col-price').checked;
        customTemplate.table.columns.tax = document.getElementById('col-tax').checked;
        customTemplate.table.columns.amount = document.getElementById('col-amount').checked;

        // Footer section
        customTemplate.footer.text = document.getElementById('footer-text').value;
        customTemplate.footer.textColor = document.getElementById('footer-text-color').value;
        customTemplate.footer.fontSize = parseInt(document.getElementById('footer-font-size').value);
        customTemplate.footer.showPageNumbers = document.getElementById('show-page-numbers').checked;

        // Fonts section
        customTemplate.fonts.primary = document.getElementById('primary-font').value;
        customTemplate.fonts.sizeNormal = parseInt(document.getElementById('text-size-normal').value);
        customTemplate.fonts.sizeSmall = parseInt(document.getElementById('text-size-small').value);

        // Colors section
        customTemplate.colors.primary = document.getElementById('primary-color').value;
        customTemplate.colors.secondary = document.getElementById('secondary-color').value;
        customTemplate.colors.accent = document.getElementById('accent-color').value;
        customTemplate.colors.text = document.getElementById('text-color').value;
        customTemplate.colors.background = document.getElementById('background-color').value;
    }

    function updateTemplatePreview() {
        const previewContainer = document.getElementById('template-preview-container');

        // Create a temporary template object with current form values
        const tempTemplate = JSON.parse(JSON.stringify(customTemplate));

        // Update with current form values without saving to the main template
        tempTemplate.name = document.getElementById('template-name').value || 'Custom Template';

        // General section
        tempTemplate.general.pageSize = document.getElementById('page-size').value;
        tempTemplate.general.orientation = document.getElementById('page-orientation').value;
        tempTemplate.general.margins.top = parseInt(document.getElementById('margin-top').value);
        tempTemplate.general.margins.right = parseInt(document.getElementById('margin-right').value);
        tempTemplate.general.margins.bottom = parseInt(document.getElementById('margin-bottom').value);
        tempTemplate.general.margins.left = parseInt(document.getElementById('margin-left').value);

        // Header section
        tempTemplate.header.height = parseInt(document.getElementById('header-height').value);
        tempTemplate.header.backgroundColor = document.getElementById('header-bg-color').value;
        tempTemplate.header.title = document.getElementById('invoice-title').value;
        tempTemplate.header.titleColor = document.getElementById('invoice-title-color').value;
        tempTemplate.header.titleSize = parseInt(document.getElementById('invoice-title-size').value);
        tempTemplate.header.companyInfoAlign = document.getElementById('company-info-align').value;
        tempTemplate.header.companyNameSize = parseInt(document.getElementById('company-name-size').value);

        // Table section
        tempTemplate.table.style = document.getElementById('table-style').value;
        tempTemplate.table.headerBackground = document.getElementById('table-header-bg').value;
        tempTemplate.table.headerTextColor = document.getElementById('table-header-text').value;
        tempTemplate.table.alternatingRows = document.getElementById('table-row-alt').checked;
        tempTemplate.table.alternateRowColor = document.getElementById('table-row-alt-color').value;
        tempTemplate.table.borderColor = document.getElementById('table-border-color').value;
        tempTemplate.table.borderWidth = parseFloat(document.getElementById('table-border-width').value);
        tempTemplate.table.columns.item = document.getElementById('col-item').checked;
        tempTemplate.table.columns.quantity = document.getElementById('col-quantity').checked;
        tempTemplate.table.columns.price = document.getElementById('col-price').checked;
        tempTemplate.table.columns.tax = document.getElementById('col-tax').checked;
        tempTemplate.table.columns.amount = document.getElementById('col-amount').checked;

        // Footer section
        tempTemplate.footer.text = document.getElementById('footer-text').value;
        tempTemplate.footer.textColor = document.getElementById('footer-text-color').value;
        tempTemplate.footer.fontSize = parseInt(document.getElementById('footer-font-size').value);
        tempTemplate.footer.showPageNumbers = document.getElementById('show-page-numbers').checked;

        // Fonts section
        tempTemplate.fonts.primary = document.getElementById('primary-font').value;
        tempTemplate.fonts.sizeNormal = parseInt(document.getElementById('text-size-normal').value);
        tempTemplate.fonts.sizeSmall = parseInt(document.getElementById('text-size-small').value);

        // Colors section
        tempTemplate.colors.primary = document.getElementById('primary-color').value;
        tempTemplate.colors.secondary = document.getElementById('secondary-color').value;
        tempTemplate.colors.accent = document.getElementById('accent-color').value;
        tempTemplate.colors.text = document.getElementById('text-color').value;
        tempTemplate.colors.background = document.getElementById('background-color').value;

        // Generate sample invoice data for preview
        const sampleData = getSampleInvoiceData();

        // Generate HTML preview based on the template
        const previewHtml = generateTemplatePreview(tempTemplate, sampleData);

        // Update the preview container
        previewContainer.innerHTML = previewHtml;

        // Apply CSS styles based on template
        applyTemplateStyles(tempTemplate);
    }

    function getSampleInvoiceData() {
        // Return sample invoice data for preview
        return {
            invoiceNumber: 'INV-001',
            invoiceDate: '2023-06-15',
            dueDate: '2023-07-15',
            business: {
                name: 'Your Business Name',
                address: '123 Business St\nCity, State 12345',
                phone: '(*************',
                email: '<EMAIL>',
                website: 'www.yourbusiness.com'
            },
            client: {
                name: 'Client Name',
                address: '456 Client Ave\nClient City, State 67890',
                phone: '(*************',
                email: '<EMAIL>'
            },
            items: [
                {
                    description: 'Product or Service 1\nAdditional description line',
                    quantity: 2,
                    price: 100.00,
                    taxRate: 0,
                    amount: 200.00,
                    tax: 0.00
                },
                {
                    description: 'Product or Service 2',
                    quantity: 1,
                    price: 50.00,
                    taxRate: 0,
                    amount: 50.00,
                    tax: 0.00
                }
            ],
            subtotal: 250.00,
            taxTotal: 0.00,
            grandTotal: 250.00,
            notesTitle: 'Notes',
            notes: 'Thank you for your business!\nPayment is due within 30 days.',
            currency: 'USD',
            currencySymbol: '$'
        };
    }

    function generateTemplatePreview(template) {
        // Generate HTML preview based on template and data
        // This is a simplified version - in a real implementation, this would be more complex
        console.log('Generating preview with template:', template.name);
        // We're not using the data parameter in this simplified version, but it would be used in a real implementation

        // Get table border style based on table style
        let tableBorderStyle = '';
        let cellBorderStyle = '';

        switch(template.table.style) {
            case 'classic':
                // All borders
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = `border: ${template.table.borderWidth}px solid ${template.table.borderColor};`;
                break;
            case 'modern':
                // Header and horizontal lines only
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = `border-top: ${template.table.borderWidth}px solid ${template.table.borderColor}; border-bottom: ${template.table.borderWidth}px solid ${template.table.borderColor};`;
                break;
            case 'minimal':
                // Horizontal lines only
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = `border-bottom: ${template.table.borderWidth}px solid ${template.table.borderColor};`;
                break;
            case 'none':
                // No borders
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = ``;
                break;
            default:
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = `border: ${template.table.borderWidth}px solid ${template.table.borderColor};`;
        }

        let html = `
            <div class="invoice-preview-wrapper" style="background-color: ${template.colors.background}; color: ${template.colors.text};">
                <div class="invoice-header" style="background-color: ${template.header.backgroundColor}; height: ${template.header.height}px;">
                    <h1 style="color: ${template.header.titleColor}; font-size: ${template.header.titleSize}px;">${template.header.title}</h1>
                    <div class="business-info" style="text-align: ${template.header.companyInfoAlign};">
                        <h2 style="font-size: ${template.header.companyNameSize}px;">Your Business Name</h2>
                        <p>123 Business St<br>City, State 12345</p>
                        <p>Phone: (*************</p>
                        <p>Email: <EMAIL></p>
                    </div>
                </div>

                <div class="invoice-body">
                    <div class="invoice-info">
                        <div class="client-info">
                            <h3>Bill To:</h3>
                            <p>Client Name<br>456 Client Ave<br>Client City, State 67890</p>
                            <p>Phone: (*************</p>
                            <p>Email: <EMAIL></p>
                        </div>
                        <div class="invoice-details">
                            <p><strong>Invoice Number:</strong> INV-001</p>
                            <p><strong>Invoice Date:</strong> 2023-06-15</p>
                            <p><strong>Due Date:</strong> 2023-07-15</p>
                        </div>
                    </div>

                    <div class="invoice-items">
                        <table style="${tableBorderStyle} width: 100%;">
                            <thead>
                                <tr style="background-color: ${template.table.headerBackground}; color: ${template.table.headerTextColor};">
                                    ${template.table.columns.item ? `<th style="padding: 10px; ${cellBorderStyle}">Description</th>` : ''}
                                    ${template.table.columns.quantity ? `<th style="padding: 10px; ${cellBorderStyle}">Quantity</th>` : ''}
                                    ${template.table.columns.price ? `<th style="padding: 10px; ${cellBorderStyle}">Unit Price</th>` : ''}
                                    ${template.table.columns.tax ? `<th style="padding: 10px; ${cellBorderStyle}">Tax (%)</th>` : ''}
                                    ${template.table.columns.amount ? `<th style="padding: 10px; ${cellBorderStyle}">Amount</th>` : ''}
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    ${template.table.columns.item ? `<td style="padding: 10px; ${cellBorderStyle}">Product or Service 1<br>Additional description line</td>` : ''}
                                    ${template.table.columns.quantity ? `<td style="padding: 10px; ${cellBorderStyle}">2</td>` : ''}
                                    ${template.table.columns.price ? `<td style="padding: 10px; ${cellBorderStyle}">$100.00</td>` : ''}
                                    ${template.table.columns.tax ? `<td style="padding: 10px; ${cellBorderStyle}">10%</td>` : ''}
                                    ${template.table.columns.amount ? `<td style="padding: 10px; ${cellBorderStyle}">$200.00</td>` : ''}
                                </tr>
                                <tr style="${template.table.alternatingRows ? `background-color: ${template.table.alternateRowColor};` : ''}">
                                    ${template.table.columns.item ? `<td style="padding: 10px; ${cellBorderStyle}">Product or Service 2</td>` : ''}
                                    ${template.table.columns.quantity ? `<td style="padding: 10px; ${cellBorderStyle}">1</td>` : ''}
                                    ${template.table.columns.price ? `<td style="padding: 10px; ${cellBorderStyle}">$50.00</td>` : ''}
                                    ${template.table.columns.tax ? `<td style="padding: 10px; ${cellBorderStyle}">10%</td>` : ''}
                                    ${template.table.columns.amount ? `<td style="padding: 10px; ${cellBorderStyle}">$50.00</td>` : ''}
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="invoice-summary">
                        <div class="summary-row">
                            <span>Subtotal:</span>
                            <span>$250.00</span>
                        </div>
                        <div class="summary-row">
                            <span>Tax:</span>
                            <span>$25.00</span>
                        </div>
                        <div class="summary-row total">
                            <span>Total USD:</span>
                            <span>$275.00</span>
                        </div>
                    </div>

                    <div class="invoice-notes">
                        <h3>Notes</h3>
                        <p>Thank you for your business!<br>Payment is due within 30 days.</p>
                    </div>
                </div>

                <div class="invoice-footer" style="color: ${template.footer.textColor}; font-size: ${template.footer.fontSize}px;">
                    <p>${template.footer.text}</p>
                    ${template.footer.showPageNumbers ? '<p>Page 1 of 1</p>' : ''}
                </div>
            </div>
        `;

        return html;
    }

    function applyTemplateStyles(template) {
        // Apply CSS styles to the preview based on template settings
        const previewContainer = document.getElementById('template-preview-container');

        // Apply font styles
        previewContainer.style.fontFamily = template.fonts.primary;
        previewContainer.style.fontSize = template.fonts.sizeNormal + 'px';

        // Apply page orientation
        if (template.general.orientation === 'landscape') {
            previewContainer.style.width = '100%';
            previewContainer.style.maxWidth = '800px';
            previewContainer.style.aspectRatio = '1.414 / 1';
        } else { // portrait
            previewContainer.style.width = '100%';
            previewContainer.style.maxWidth = '600px';
            previewContainer.style.aspectRatio = '1 / 1.414';
        }

        // Apply page size class
        previewContainer.className = 'preview-container';
        previewContainer.classList.add(`page-${template.general.pageSize}`);
        previewContainer.classList.add(`orientation-${template.general.orientation}`);
    }

    function applyColorPreset(presetName) {
        // Apply color preset to the form inputs
        const presets = {
            blue: {
                primary: '#3498db',
                secondary: '#2ecc71',
                accent: '#e74c3c'
            },
            green: {
                primary: '#2ecc71',
                secondary: '#3498db',
                accent: '#f39c12'
            },
            purple: {
                primary: '#9b59b6',
                secondary: '#3498db',
                accent: '#e74c3c'
            },
            red: {
                primary: '#e74c3c',
                secondary: '#f39c12',
                accent: '#3498db'
            },
            orange: {
                primary: '#f39c12',
                secondary: '#e74c3c',
                accent: '#3498db'
            },
            teal: {
                primary: '#1abc9c',
                secondary: '#3498db',
                accent: '#f39c12'
            }
        };

        if (presets[presetName]) {
            document.getElementById('primary-color').value = presets[presetName].primary;
            document.getElementById('secondary-color').value = presets[presetName].secondary;
            document.getElementById('accent-color').value = presets[presetName].accent;

            // Update the template colors
            customTemplate.colors.primary = presets[presetName].primary;
            customTemplate.colors.secondary = presets[presetName].secondary;
            customTemplate.colors.accent = presets[presetName].accent;

            // Update header colors
            document.getElementById('invoice-title-color').value = presets[presetName].primary;
            customTemplate.header.titleColor = presets[presetName].primary;

            // Update table header background
            document.getElementById('table-header-bg').value = presets[presetName].primary;
            customTemplate.table.headerBackground = presets[presetName].primary;

            // Set status
            setStatus(`Applied ${presetName} color preset.`, 'info');
        }
    }

    // Custom Templates Management Functions
    function loadCustomTemplates() {
        console.log('Loading custom templates');
        const customTemplatesContainer = document.getElementById('custom-templates');
        const noCustomTemplatesMsg = customTemplatesContainer.querySelector('.no-custom-templates');

        // Get all saved templates
        const savedTemplates = getAllSavedTemplates();
        console.log('Found saved templates:', savedTemplates);

        // Clear existing templates
        while (customTemplatesContainer.firstChild) {
            if (customTemplatesContainer.firstChild.classList &&
                customTemplatesContainer.firstChild.classList.contains('no-custom-templates')) {
                break;
            }
            customTemplatesContainer.removeChild(customTemplatesContainer.firstChild);
        }

        // If no templates, show message
        if (savedTemplates.length === 0) {
            if (noCustomTemplatesMsg) {
                noCustomTemplatesMsg.style.display = 'block';
            }
            return;
        }

        // Hide no templates message
        if (noCustomTemplatesMsg) {
            noCustomTemplatesMsg.style.display = 'none';
        }

        // Add each template to the grid
        savedTemplates.forEach(template => {
            const templateCard = createTemplateCard(template);
            customTemplatesContainer.insertBefore(templateCard, noCustomTemplatesMsg);
        });

        // Setup event handlers for the template cards
        setupTemplateCardEvents();
    }

    function getAllSavedTemplates() {
        const templates = [];
        const customTemplate = loadCustomTemplate();

        if (customTemplate) {
            templates.push(customTemplate);
        }

        // In a more advanced implementation, we would load multiple templates
        // For now, we just have one custom template

        return templates;
    }

    function createTemplateCard(template) {
        const card = document.createElement('div');
        card.className = 'template-card custom-template-card';
        card.setAttribute('data-template', template.name);
        card.setAttribute('data-template-type', 'custom');

        // Generate a preview image based on the template
        const previewHtml = `
            <div class="template-preview">
                <div style="background-color: ${template.colors.background}; color: ${template.colors.text}; padding: 10px; width: 100%; height: 100%; display: flex; flex-direction: column;">
                    <div style="background-color: ${template.header.backgroundColor}; padding: 10px; margin-bottom: 10px;">
                        <h3 style="color: ${template.header.titleColor}; margin: 0;">${template.header.title}</h3>
                    </div>
                    <div style="flex: 1; display: flex; flex-direction: column; justify-content: space-between;">
                        <div style="background-color: ${template.table.headerBackground}; color: ${template.table.headerTextColor}; padding: 5px;">
                            Table Header
                        </div>
                        <div style="padding: 5px; ${template.table.alternatingRows ? `background-color: ${template.table.alternateRowColor};` : ''}">
                            Table Row
                        </div>
                        <div style="color: ${template.footer.textColor}; font-size: ${template.footer.fontSize}px; padding: 5px; text-align: center;">
                            ${template.footer.text}
                        </div>
                    </div>
                </div>
            </div>
        `;

        card.innerHTML = `
            ${previewHtml}
            <div class="template-info">
                <h4>${template.name}</h4>
                <p>Custom template</p>
            </div>
            <div class="template-actions">
                <button class="btn-edit-template" title="Edit Template">
                    <i class="fas fa-edit"></i>
                </button>
                <button class="btn-delete-template" title="Delete Template">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;

        return card;
    }

    function setupTemplateCardEvents() {
        // Template selection
        const templateCards = document.querySelectorAll('.template-card');
        templateCards.forEach(card => {
            card.addEventListener('click', function(e) {
                // Don't select if clicking on a button
                if (e.target.closest('button')) {
                    return;
                }

                templateCards.forEach(c => c.classList.remove('active'));
                this.classList.add('active');
                selectedTemplate = this.getAttribute('data-template');
                setStatus(`Template "${selectedTemplate}" selected.`, 'success');

                // Save selected template to settings
                settings.selectedTemplate = selectedTemplate;
                saveSettings(settings);
            });
        });

        // Edit template buttons
        const editButtons = document.querySelectorAll('.btn-edit-template');
        editButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent template selection

                const templateCard = this.closest('.template-card');
                const templateName = templateCard.getAttribute('data-template');
                const templateType = templateCard.getAttribute('data-template-type');

                // Navigate to customizer tab
                const customizerTabBtn = document.querySelector('.tab-button[data-tab="template-customizer"]');
                customizerTabBtn.click();

                // Load the template for editing
                if (templateType === 'custom') {
                    // Load the custom template
                    const savedTemplate = loadCustomTemplate();
                    if (savedTemplate && savedTemplate.name === templateName) {
                        customTemplate = savedTemplate;
                        loadTemplateIntoForm();
                        updateTemplatePreview();
                        setStatus(`Editing custom template "${templateName}".`, 'info');
                    }
                } else {
                    // Load a built-in template as a starting point
                    // In a real implementation, we would have predefined templates
                    // For now, we'll just use our default template
                    customTemplate = JSON.parse(JSON.stringify(defaultTemplate));
                    customTemplate.name = `Custom ${templateName}`;
                    loadTemplateIntoForm();
                    updateTemplatePreview();
                    setStatus(`Creating new template based on "${templateName}".`, 'info');
                }
            });
        });

        // Delete template buttons
        const deleteButtons = document.querySelectorAll('.btn-delete-template');
        deleteButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.stopPropagation(); // Prevent template selection

                const templateCard = this.closest('.template-card');
                const templateName = templateCard.getAttribute('data-template');

                if (confirm(`Are you sure you want to delete the template "${templateName}"?`)) {
                    // Delete the template
                    localStorage.removeItem('invoice-maker-custom-template');

                    // Reload templates
                    loadCustomTemplates();

                    // If this was the selected template, select the first available template
                    if (selectedTemplate === templateName) {
                        const firstTemplate = document.querySelector('.template-card');
                        if (firstTemplate) {
                            firstTemplate.click();
                        }
                    }

                    setStatus(`Template "${templateName}" deleted.`, 'info');
                }
            });
        });

        // Create new template button
        const createTemplateBtn = document.getElementById('create-template-btn');
        if (createTemplateBtn) {
            createTemplateBtn.addEventListener('click', function() {
                // Navigate to customizer tab
                const customizerTabBtn = document.querySelector('.tab-button[data-tab="template-customizer"]');
                customizerTabBtn.click();

                // Reset to default template
                customTemplate = JSON.parse(JSON.stringify(defaultTemplate));
                customTemplate.name = 'New Custom Template';
                loadTemplateIntoForm();
                updateTemplatePreview();

                setStatus('Creating new template. Customize and save when ready.', 'info');
            });
        }
    }

    function addInvoiceItem() {
        itemCounter++;
        const newRow = document.createElement('tr');
        newRow.setAttribute('data-item-id', itemCounter);

        newRow.innerHTML = `
            <td><textarea class="item-description" placeholder="Item description" rows="3"></textarea></td>
            <td><input type="number" class="item-quantity" value="1" min="1" step="1"></td>
            <td><input type="number" class="item-price" value="0.00" min="0" step="0.01"></td>
            <td><input type="number" class="item-tax" value="${settings.defaultTaxRate}" min="0" max="100" step="0.1"></td>
            <td><span class="item-amount">$0.00</span></td>
            <td>
                <button class="btn btn-danger delete-item-btn">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        invoiceItemsBody.appendChild(newRow);

        // Add event listeners to new row inputs
        const quantityInput = newRow.querySelector('.item-quantity');
        const priceInput = newRow.querySelector('.item-price');
        const taxInput = newRow.querySelector('.item-tax');
        const deleteBtn = newRow.querySelector('.delete-item-btn');

        // Calculate amount when inputs change
        quantityInput.addEventListener('input', () => calculateItemAmount(newRow));
        priceInput.addEventListener('input', () => calculateItemAmount(newRow));
        taxInput.addEventListener('input', () => calculateItemAmount(newRow));

        // Delete item
        deleteBtn.addEventListener('click', function() {
            newRow.remove();
            calculateInvoiceTotals();
        });

        // Initial calculation
        calculateItemAmount(newRow);
    }

    function calculateItemAmount(row) {
        const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
        const price = parseFloat(row.querySelector('.item-price').value) || 0;
        const amount = quantity * price;

        row.querySelector('.item-amount').textContent = formatCurrency(amount);

        // Recalculate invoice totals
        calculateInvoiceTotals();
    }

    function calculateInvoiceTotals() {
        let subtotal = 0;
        let taxTotal = 0;

        // Get all rows
        const rows = invoiceItemsBody.querySelectorAll('tr');

        rows.forEach(row => {
            const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(row.querySelector('.item-price').value) || 0;
            const taxRate = parseFloat(row.querySelector('.item-tax').value) || 0;

            const amount = quantity * price;
            const tax = amount * (taxRate / 100);

            subtotal += amount;
            taxTotal += tax;
        });

        const grandTotal = subtotal + taxTotal;

        // Update totals display
        document.getElementById('subtotal').textContent = formatCurrency(subtotal);
        document.getElementById('tax-total').textContent = formatCurrency(taxTotal);
        document.getElementById('grand-total').textContent = formatCurrency(grandTotal);
    }

    function formatCurrency(amount) {
        const currencySymbol = getCurrencySymbol(settings.defaultCurrency);
        return `${currencySymbol}${amount.toFixed(2)}`;
    }

    function getCurrencySymbol(currencyCode) {
        const currencies = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'AUD': '$',
            'CAD': '$',
            'JPY': '¥',
            'CNY': '¥',
            'INR': '₹',
            'BRL': 'R$',
            'CHF': 'Fr',
            'NZD': '$',
            'ZAR': 'R',
            'HKD': '$',
            'SGD': '$',
            'MXN': '$',
            'PHP': '₱',
            'SEK': 'kr',
            'NOK': 'kr',
            'DKK': 'kr',
            'PLN': 'zł',
            'RUB': '₽',
            'TRY': '₺',
            'AED': 'د.إ',
            'SAR': '﷼'
        };
        return currencies[currencyCode] || '$';
    }

    function generateInvoicePreview() {
        // Get form data
        const invoiceData = getInvoiceData();

        // Check if the selected template is a custom template
        const templateType = document.querySelector(`.template-card.active[data-template="${selectedTemplate}"]`)?.getAttribute('data-template-type');

        let previewHtml;
        if (templateType === 'custom') {
            // For custom templates, load the custom template from localStorage
            console.log('Using custom template for HTML generation');
            const customTemplateData = loadCustomTemplate();

            if (customTemplateData) {
                // Generate custom HTML with the custom template's styling
                previewHtml = generateCustomHtml(invoiceData, customTemplateData);
            } else {
                // Fallback to classic template if custom template can't be loaded
                console.log('Falling back to classic template for HTML generation');
                previewHtml = templates['classic'].generateHtml(invoiceData);
            }
        } else {
            // For built-in templates, use the template's HTML generation function
            previewHtml = templates[selectedTemplate].generateHtml(invoiceData);
        }

        // Insert into preview container
        invoicePreviewContainer.innerHTML = previewHtml;
    }

    function generateCustomHtml(data, template) {
        // Generate HTML based on custom template settings
        console.log('Generating HTML with custom template:', template.name);

        // Get table border style based on table style
        let tableBorderStyle = '';
        let cellBorderStyle = '';

        switch(template.table.style) {
            case 'classic':
                // All borders
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = `border: ${template.table.borderWidth}px solid ${template.table.borderColor};`;
                break;
            case 'modern':
                // Header and horizontal lines only
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = `border-top: ${template.table.borderWidth}px solid ${template.table.borderColor}; border-bottom: ${template.table.borderWidth}px solid ${template.table.borderColor};`;
                break;
            case 'minimal':
                // Horizontal lines only
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = `border-bottom: ${template.table.borderWidth}px solid ${template.table.borderColor};`;
                break;
            case 'none':
                // No borders
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = ``;
                break;
            default:
                tableBorderStyle = `border-collapse: collapse;`;
                cellBorderStyle = `border: ${template.table.borderWidth}px solid ${template.table.borderColor};`;
        }

        // Generate HTML with custom styling
        return `
            <div class="invoice-preview custom-template" style="background-color: ${template.colors.background}; color: ${template.colors.text}; font-family: ${template.fonts.primary}; font-size: ${template.fonts.sizeNormal}px;">
                <div class="invoice-header" style="background-color: ${template.header.backgroundColor}; padding: 20px; margin-bottom: 20px;">
                    <div class="invoice-title">
                        <h1 style="color: ${template.header.titleColor}; font-size: ${template.header.titleSize}px; margin: 0;">${template.header.title}</h1>
                        <div class="invoice-number">${data.invoiceNumber || 'New Invoice'}</div>
                    </div>
                    <div class="invoice-company" style="text-align: ${template.header.companyInfoAlign};">
                        <h2 style="font-size: ${template.header.companyNameSize}px;">${data.business.name || 'Your Business'}</h2>
                        <p>${data.business.address || ''}</p>
                        <p>${data.business.phone || ''}</p>
                        <p>${data.business.email || ''}</p>
                        <p>${data.business.website || ''}</p>
                    </div>
                </div>

                <div class="invoice-info" style="display: flex; justify-content: space-between; margin-bottom: 20px; padding: 0 20px;">
                    <div class="client-info">
                        <h3 style="color: ${template.colors.primary};">Bill To:</h3>
                        <h4>${data.client.name || 'Client Name'}</h4>
                        <p>${data.client.address || ''}</p>
                        <p>${data.client.phone || ''}</p>
                        <p>${data.client.email || ''}</p>
                    </div>
                    <div class="invoice-details">
                        <div class="detail-row">
                            <span>Invoice Date:</span>
                            <span>${data.invoiceDate || ''}</span>
                        </div>
                        <div class="detail-row">
                            <span>Due Date:</span>
                            <span>${data.dueDate || ''}</span>
                        </div>
                    </div>
                </div>

                <div class="invoice-items" style="margin-bottom: 20px; padding: 0 20px;">
                    <table style="${tableBorderStyle} width: 100%;">
                        <thead>
                            <tr style="background-color: ${template.table.headerBackground}; color: ${template.table.headerTextColor};">
                                ${template.table.columns.item ? `<th style="padding: 10px; ${cellBorderStyle}">Description</th>` : ''}
                                ${template.table.columns.quantity ? `<th style="padding: 10px; ${cellBorderStyle}">Quantity</th>` : ''}
                                ${template.table.columns.price ? `<th style="padding: 10px; ${cellBorderStyle}">Unit Price</th>` : ''}
                                ${template.table.columns.tax ? `<th style="padding: 10px; ${cellBorderStyle}">Tax (%)</th>` : ''}
                                ${template.table.columns.amount ? `<th style="padding: 10px; ${cellBorderStyle}">Amount</th>` : ''}
                            </tr>
                        </thead>
                        <tbody>
                            ${data.items.map((item, index) => `
                                <tr style="${template.table.alternatingRows && index % 2 === 1 ? `background-color: ${template.table.alternateRowColor};` : ''}">
                                    ${template.table.columns.item ? `<td style="padding: 10px; ${cellBorderStyle}">${item.description || 'Item'}</td>` : ''}
                                    ${template.table.columns.quantity ? `<td style="padding: 10px; ${cellBorderStyle}">${item.quantity}</td>` : ''}
                                    ${template.table.columns.price ? `<td style="padding: 10px; ${cellBorderStyle}">${data.currencySymbol}${item.price.toFixed(2)}</td>` : ''}
                                    ${template.table.columns.tax ? `<td style="padding: 10px; ${cellBorderStyle}">${item.taxRate}%</td>` : ''}
                                    ${template.table.columns.amount ? `<td style="padding: 10px; ${cellBorderStyle}">${data.currencySymbol}${item.amount.toFixed(2)}</td>` : ''}
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>

                <div class="invoice-summary" style="margin-left: auto; width: 300px; padding: 0 20px;">
                    <div class="summary-row" style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>Subtotal:</span>
                        <span>${data.currencySymbol}${data.subtotal.toFixed(2)}</span>
                    </div>
                    <div class="summary-row" style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                        <span>Tax:</span>
                        <span>${data.currencySymbol}${data.taxTotal.toFixed(2)}</span>
                    </div>
                    <div class="summary-row total" style="display: flex; justify-content: space-between; font-weight: bold; color: ${template.colors.primary}; margin-top: 10px; border-top: 2px solid ${template.colors.primary}; padding-top: 5px;">
                        <span>Total ${data.currency}:</span>
                        <span>${data.currencySymbol}${data.grandTotal.toFixed(2)}</span>
                    </div>
                </div>

                ${data.notes ? `
                    <div class="invoice-notes" style="margin-top: 30px; padding: 0 20px;">
                        <h3 style="color: ${template.colors.primary};">${data.notesTitle || 'Notes'}</h3>
                        <p>${data.notes}</p>
                    </div>
                ` : ''}

                <div class="invoice-footer" style="margin-top: 50px; text-align: center; color: ${template.footer.textColor}; font-size: ${template.footer.fontSize}px; padding: 20px;">
                    <p>${template.footer.text}</p>
                    ${template.footer.showPageNumbers ? '<p>Page 1 of 1</p>' : ''}
                </div>
            </div>
        `;
    }

    function getInvoiceData() {
        // Collect all form data
        const invoiceNumber = document.getElementById('invoice-number').value;
        const invoiceDate = document.getElementById('invoice-date').value;
        const dueDate = document.getElementById('due-date').value;

        const businessName = document.getElementById('business-name').value;
        const businessAddress = document.getElementById('business-address').value;
        const businessPhone = document.getElementById('business-phone').value;
        const businessEmail = document.getElementById('business-email').value;
        const businessWebsite = document.getElementById('business-website').value;

        const clientName = document.getElementById('client-name').value;
        const clientAddress = document.getElementById('client-address').value;
        const clientPhone = document.getElementById('client-phone').value;
        const clientEmail = document.getElementById('client-email').value;

        const notesTitle = document.getElementById('notes-title').value || 'Notes';
        const notes = document.getElementById('invoice-notes').value;

        // Collect items
        const items = [];
        const rows = invoiceItemsBody.querySelectorAll('tr');

        rows.forEach(row => {
            const description = row.querySelector('.item-description').value;
            const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(row.querySelector('.item-price').value) || 0;
            const taxRate = parseFloat(row.querySelector('.item-tax').value) || 0;

            const amount = quantity * price;
            const tax = amount * (taxRate / 100);

            items.push({
                description,
                quantity,
                price,
                taxRate,
                amount,
                tax
            });
        });

        // Calculate totals
        const subtotal = items.reduce((sum, item) => sum + item.amount, 0);
        const taxTotal = items.reduce((sum, item) => sum + item.tax, 0);
        const grandTotal = subtotal + taxTotal;

        return {
            invoiceNumber,
            invoiceDate,
            dueDate,
            business: {
                name: businessName,
                address: businessAddress,
                phone: businessPhone,
                email: businessEmail,
                website: businessWebsite
            },
            client: {
                name: clientName,
                address: clientAddress,
                phone: clientPhone,
                email: clientEmail
            },
            items,
            subtotal,
            taxTotal,
            grandTotal,
            notesTitle,
            notes,
            currency: settings.defaultCurrency,
            currencySymbol: getCurrencySymbol(settings.defaultCurrency)
        };
    }

    function exportToPdf() {
        try {
            // Get invoice data
            const invoiceData = getInvoiceData();

            // Create PDF document with settings from the template
            const { jsPDF } = window.jspdf;

            // Check if the selected template is a custom template
            const templateType = document.querySelector(`.template-card.active[data-template="${selectedTemplate}"]`)?.getAttribute('data-template-type');

            if (templateType === 'custom') {
                // For custom templates, load the custom template from localStorage
                console.log('Using custom template for PDF generation');
                const customTemplateData = loadCustomTemplate();

                if (customTemplateData) {
                    // Use the custom template's settings for the PDF
                    const doc = new jsPDF({
                        orientation: customTemplateData.general.orientation,
                        unit: 'mm',
                        format: customTemplateData.general.pageSize
                    });

                    // Generate PDF with custom template styling
                    generateCustomPdf(doc, invoiceData, customTemplateData);

                    // Generate filename with invoice number and date range
                    const filename = generateInvoiceFilename(invoiceData);

                    // Save the PDF
                    doc.save(filename);
                    setStatus('PDF exported successfully with custom template.', 'success');
                } else {
                    // Fallback to classic template if custom template can't be loaded
                    const doc = new jsPDF({
                        orientation: settings.pdfOrientation,
                        unit: 'mm',
                        format: settings.pdfPageSize
                    });
                    templates['classic'].generatePdf(doc, invoiceData);
                    // Generate filename with invoice number and date range
                    const filename = generateInvoiceFilename(invoiceData);

                    doc.save(filename);
                    setStatus('PDF exported successfully with classic template (fallback).', 'info');
                }
            } else {
                // For built-in templates, use the template's PDF generation function
                const doc = new jsPDF({
                    orientation: settings.pdfOrientation,
                    unit: 'mm',
                    format: settings.pdfPageSize
                });
                templates[selectedTemplate].generatePdf(doc, invoiceData);
                // Generate filename with invoice number and date range
                const filename = generateInvoiceFilename(invoiceData);

                doc.save(filename);
                setStatus('PDF exported successfully.', 'success');
            }
        } catch (error) {
            console.error('PDF Export Error:', error);
            setStatus(`Error exporting PDF: ${error.message}`, 'error');
        }
    }

    function generateCustomPdf(doc, data, template) {
        // Generate PDF based on custom template settings
        console.log('Generating PDF with custom template:', template.name);

        // Set font
        doc.setFont(template.fonts.primary || 'helvetica');

        // Apply header styling
        if (template.header.backgroundColor !== '#ffffff') {
            // Convert hex to RGB for jsPDF
            const headerBgColor = hexToRgb(template.header.backgroundColor);
            doc.setFillColor(headerBgColor.r, headerBgColor.g, headerBgColor.b);
            doc.rect(0, 0, doc.internal.pageSize.width, template.header.height, 'F');
        }

        // Header title
        doc.setFontSize(template.header.titleSize);
        const titleColor = hexToRgb(template.header.titleColor);
        doc.setTextColor(titleColor.r, titleColor.g, titleColor.b);
        doc.text(template.header.title, 20, 20);

        // Invoice number
        doc.setFontSize(template.fonts.sizeNormal);
        doc.text(`${data.invoiceNumber || 'New Invoice'}`, 20, 30);

        // Business info
        const textColor = hexToRgb(template.colors.text);
        doc.setTextColor(textColor.r, textColor.g, textColor.b);
        doc.setFontSize(template.header.companyNameSize);

        // Position business info based on alignment
        const businessInfoX = template.header.companyInfoAlign === 'right' ? 120 : 20;
        doc.text(data.business.name || 'Your Business', businessInfoX, 20);

        doc.setFontSize(template.fonts.sizeNormal);

        // Handle business info with dynamic positioning
        let businessInfoY = 30;
        const lineSpacing = 7;

        if (data.business.address && data.business.address.trim()) {
            doc.text(data.business.address.split('\n'), businessInfoX, businessInfoY);
            businessInfoY += data.business.address.split('\n').length * lineSpacing;
        }

        if (data.business.phone && data.business.phone.trim()) {
            doc.text(data.business.phone, businessInfoX, businessInfoY);
            businessInfoY += lineSpacing;
        }

        if (data.business.email && data.business.email.trim()) {
            doc.text(data.business.email, businessInfoX, businessInfoY);
            businessInfoY += lineSpacing;
        }

        if (data.business.website && data.business.website.trim()) {
            doc.text(data.business.website, businessInfoX, businessInfoY);
        }

        // Client info
        const primaryColor = hexToRgb(template.colors.primary);
        doc.setFontSize(template.fonts.sizeNormal + 2);
        doc.setTextColor(primaryColor.r, primaryColor.g, primaryColor.b);
        doc.text('Bill To:', 20, 60);

        doc.setFontSize(template.fonts.sizeNormal);
        doc.setTextColor(textColor.r, textColor.g, textColor.b);
        doc.text(data.client.name || 'Client Name', 20, 67);

        // Handle client info with dynamic positioning
        let clientInfoY = 74;

        if (data.client.address && data.client.address.trim()) {
            doc.text(data.client.address.split('\n'), 20, clientInfoY);
            clientInfoY += data.client.address.split('\n').length * lineSpacing;
        }

        if (data.client.phone && data.client.phone.trim()) {
            doc.text(data.client.phone, 20, clientInfoY);
            clientInfoY += lineSpacing;
        }

        if (data.client.email && data.client.email.trim()) {
            doc.text(data.client.email, 20, clientInfoY);
        }

        // Invoice details
        doc.setFontSize(template.fonts.sizeNormal);
        doc.text('Invoice Date:', 120, 67);
        doc.text(data.invoiceDate || '', 160, 67);

        doc.text('Due Date:', 120, 74);
        doc.text(data.dueDate || '', 160, 74);

        // Items table
        const tableTop = 100;
        const tableHeaders = [];
        const tablePositions = [];
        let position = 22;

        // Only include columns that are enabled in the template
        if (template.table.columns.item) {
            tableHeaders.push('Description');
            tablePositions.push(position);
            position += 58;
        }

        if (template.table.columns.quantity) {
            tableHeaders.push('Qty');
            tablePositions.push(position);
            position += 20;
        }

        if (template.table.columns.price) {
            tableHeaders.push('Unit Price');
            tablePositions.push(position);
            position += 30;
        }

        if (template.table.columns.tax) {
            tableHeaders.push('Tax (%)');
            tablePositions.push(position);
            position += 30;
        }

        if (template.table.columns.amount) {
            tableHeaders.push('Amount');
            tablePositions.push(position);
        }

        // Prepare table data
        const tableData = data.items.map(item => {
            const row = [];
            if (template.table.columns.item) row.push(item.description || 'Item');
            if (template.table.columns.quantity) row.push(item.quantity.toString());
            if (template.table.columns.price) row.push(`${item.price.toFixed(2)}`);
            if (template.table.columns.tax) row.push(`${item.taxRate}%`);
            if (template.table.columns.amount) row.push(`${item.amount.toFixed(2)}`);
            return row;
        });

        // Draw table header
        const headerBgColor = hexToRgb(template.table.headerBackground);
        const headerTextColor = hexToRgb(template.table.headerTextColor);

        // Initialize variables used across all table styles
        let yPos = tableTop;
        let borderColor;

        // Apply table style based on template
        switch(template.table.style) {
            case 'classic':
                // Draw header background
                doc.setFillColor(headerBgColor.r, headerBgColor.g, headerBgColor.b);
                doc.rect(20, tableTop - 10, 170, 10, 'F');

                // Draw header text
                doc.setTextColor(headerTextColor.r, headerTextColor.g, headerTextColor.b);
                doc.setFontSize(template.fonts.sizeNormal);
                tableHeaders.forEach((header, i) => {
                    doc.text(header, tablePositions[i], tableTop - 3);
                });

                // Reset position for rows
                yPos = tableTop;
                doc.setTextColor(textColor.r, textColor.g, textColor.b);

                // Get border color
                borderColor = hexToRgb(template.table.borderColor);

                tableData.forEach((row, i) => {
                    // Adjust row height for multi-line descriptions
                    const descLines = row[0].split('\n');
                    const lineCount = Math.min(descLines.length, 6);
                    const rowHeight = Math.max(10, lineCount * 5 + 5);

                    // Alternate row background
                    if (template.table.alternatingRows && i % 2 === 1) {
                        const altRowColor = hexToRgb(template.table.alternateRowColor);
                        doc.setFillColor(altRowColor.r, altRowColor.g, altRowColor.b);
                        doc.rect(20, yPos, 170, rowHeight, 'F');
                    }

                    // Draw cell content
                    row.forEach((cell, j) => {
                        if (j === 0 && cell.includes('\n')) {
                            // Handle multi-line description
                            const lines = cell.split('\n');
                            lines.forEach((line, lineIndex) => {
                                doc.text(line.substring(0, 30), tablePositions[j], yPos + 5 + (lineIndex * 5));
                            });
                        } else {
                            doc.text(cell, tablePositions[j], yPos + 5);
                        }
                    });

                    yPos += rowHeight;

                    // Draw horizontal line after each row
                    doc.setDrawColor(borderColor.r, borderColor.g, borderColor.b);
                    doc.setLineWidth(template.table.borderWidth * 0.1); // Convert px to mm (approx)
                    doc.line(20, yPos, 190, yPos);
                });

                // Draw table border
                doc.setDrawColor(borderColor.r, borderColor.g, borderColor.b);
                doc.rect(20, tableTop - 10, 170, yPos - tableTop + 10, 'S');
                break;

            case 'modern':
                // Draw header background
                doc.setFillColor(headerBgColor.r, headerBgColor.g, headerBgColor.b);
                doc.rect(20, tableTop - 10, 170, 10, 'F');

                // Draw header text
                doc.setTextColor(headerTextColor.r, headerTextColor.g, headerTextColor.b);
                doc.setFontSize(template.fonts.sizeNormal);
                tableHeaders.forEach((header, i) => {
                    doc.text(header, tablePositions[i], tableTop - 3);
                });

                // Reset position for rows
                yPos = tableTop;
                doc.setTextColor(textColor.r, textColor.g, textColor.b);

                // Get border color
                borderColor = hexToRgb(template.table.borderColor);

                tableData.forEach((row, i) => {
                    // Adjust row height for multi-line descriptions
                    const descLines = row[0].split('\n');
                    const lineCount = Math.min(descLines.length, 6);
                    const rowHeight = Math.max(10, lineCount * 5 + 5);

                    // Alternate row background
                    if (template.table.alternatingRows && i % 2 === 1) {
                        const altRowColor = hexToRgb(template.table.alternateRowColor);
                        doc.setFillColor(altRowColor.r, altRowColor.g, altRowColor.b);
                        doc.rect(20, yPos, 170, rowHeight, 'F');
                    }

                    // Draw cell content
                    row.forEach((cell, j) => {
                        if (j === 0 && cell.includes('\n')) {
                            // Handle multi-line description
                            const lines = cell.split('\n');
                            lines.forEach((line, lineIndex) => {
                                doc.text(line.substring(0, 30), tablePositions[j], yPos + 5 + (lineIndex * 5));
                            });
                        } else {
                            doc.text(cell, tablePositions[j], yPos + 5);
                        }
                    });

                    yPos += rowHeight;

                    // Draw horizontal line after each row
                    doc.setDrawColor(borderColor.r, borderColor.g, borderColor.b);
                    doc.setLineWidth(template.table.borderWidth * 0.1);
                    doc.line(20, yPos, 190, yPos);
                });
                break;

            case 'minimal':
                // Draw header text
                doc.setTextColor(headerTextColor.r, headerTextColor.g, headerTextColor.b);
                doc.setFontSize(template.fonts.sizeNormal);
                tableHeaders.forEach((header, i) => {
                    doc.text(header, tablePositions[i], tableTop - 3);
                });

                // Get border color
                borderColor = hexToRgb(template.table.borderColor);

                // Draw horizontal line under headers
                doc.setDrawColor(borderColor.r, borderColor.g, borderColor.b);
                doc.setLineWidth(template.table.borderWidth * 0.1);
                doc.line(20, tableTop, 190, tableTop);

                // Reset position for rows
                yPos = tableTop;
                doc.setTextColor(textColor.r, textColor.g, textColor.b);

                tableData.forEach((row, _) => { // Using _ to indicate unused parameter
                    // Adjust row height for multi-line descriptions
                    const descLines = row[0].split('\n');
                    const lineCount = Math.min(descLines.length, 6);
                    const rowHeight = Math.max(10, lineCount * 5 + 5);

                    // Draw cell content
                    row.forEach((cell, j) => {
                        if (j === 0 && cell.includes('\n')) {
                            // Handle multi-line description
                            const lines = cell.split('\n');
                            lines.forEach((line, lineIndex) => {
                                doc.text(line.substring(0, 30), tablePositions[j], yPos + 5 + (lineIndex * 5));
                            });
                        } else {
                            doc.text(cell, tablePositions[j], yPos + 5);
                        }
                    });

                    yPos += rowHeight;

                    // Draw horizontal line after each row
                    doc.setDrawColor(borderColor.r, borderColor.g, borderColor.b);
                    doc.line(20, yPos, 190, yPos);
                });
                break;

            case 'none':
            default:
                // Draw header text
                doc.setTextColor(headerTextColor.r, headerTextColor.g, headerTextColor.b);
                doc.setFontSize(template.fonts.sizeNormal);
                tableHeaders.forEach((header, i) => {
                    doc.text(header, tablePositions[i], tableTop - 3);
                });

                // Reset position for rows
                yPos = tableTop;
                doc.setTextColor(textColor.r, textColor.g, textColor.b);

                tableData.forEach((row, i) => {
                    // Adjust row height for multi-line descriptions
                    const descLines = row[0].split('\n');
                    const lineCount = Math.min(descLines.length, 6);
                    const rowHeight = Math.max(10, lineCount * 5 + 5);

                    // Alternate row background
                    if (template.table.alternatingRows && i % 2 === 1) {
                        const altRowColor = hexToRgb(template.table.alternateRowColor);
                        doc.setFillColor(altRowColor.r, altRowColor.g, altRowColor.b);
                        doc.rect(20, yPos, 170, rowHeight, 'F');
                    }

                    // Draw cell content
                    row.forEach((cell, j) => {
                        if (j === 0 && cell.includes('\n')) {
                            // Handle multi-line description
                            const lines = cell.split('\n');
                            lines.forEach((line, lineIndex) => {
                                doc.text(line.substring(0, 30), tablePositions[j], yPos + 5 + (lineIndex * 5));
                            });
                        } else {
                            doc.text(cell, tablePositions[j], yPos + 5);
                        }
                    });

                    yPos += rowHeight;
                });
                break;
        }

        // Summary
        const summaryX = 130;
        let summaryY = yPos + 20;

        doc.setFontSize(template.fonts.sizeNormal);
        doc.setTextColor(textColor.r, textColor.g, textColor.b);
        // Get the summary spacing and alignment from settings
        const summarySpacing = settings.summarySpacing || 40; // Default to 40mm if not set
        const summaryAlignment = settings.summaryAlignment || 'right'; // Default to right alignment

        // Calculate the position for the amount based on the user's spacing preference
        const amountX = summaryX + summarySpacing;

        // Format all amounts to ensure consistent decimal places
        const subtotalText = `${data.subtotal.toFixed(2)}`;
        const taxText = `${data.taxTotal.toFixed(2)}`;
        const totalText = `Total ${data.currency}:`;
        const grandTotalText = `${data.grandTotal.toFixed(2)}`;

        // Subtotal row
        doc.text('Subtotal:', summaryX, summaryY);
        if (summaryAlignment === 'right') {
            doc.text(subtotalText, amountX, summaryY, { align: 'right' });
        } else {
            doc.text(subtotalText, amountX, summaryY);
        }

        summaryY += 7;

        // Tax row
        doc.text('Tax:', summaryX, summaryY);
        if (summaryAlignment === 'right') {
            doc.text(taxText, amountX, summaryY, { align: 'right' });
        } else {
            doc.text(taxText, amountX, summaryY);
        }

        summaryY += 7;
        doc.setFontSize(template.fonts.sizeNormal + 2);
        doc.setTextColor(primaryColor.r, primaryColor.g, primaryColor.b);

        // Total row
        doc.text(totalText, summaryX, summaryY);
        if (summaryAlignment === 'right') {
            doc.text(grandTotalText, amountX, summaryY, { align: 'right' });
        } else {
            doc.text(grandTotalText, amountX, summaryY);
        }

        // Notes
        if (data.notes) {
            summaryY += 20;
            doc.setFontSize(template.fonts.sizeNormal + 1);
            doc.setTextColor(primaryColor.r, primaryColor.g, primaryColor.b);
            doc.text(`${data.notesTitle || 'Notes'}:`, 20, summaryY);

            summaryY += 7;
            doc.setFontSize(template.fonts.sizeNormal);
            doc.setTextColor(textColor.r, textColor.g, textColor.b);
            const notesLines = data.notes.split(/\r?\n/);
            let noteY = summaryY;

            notesLines.forEach(line => {
                doc.text(line, 20, noteY);
                noteY += 5;
            });
        }

        // Footer
        const footerColor = hexToRgb(template.footer.textColor);
        doc.setFontSize(template.footer.fontSize);
        doc.setTextColor(footerColor.r, footerColor.g, footerColor.b);
        doc.text(template.footer.text, 105, 280, { align: 'center' });

        if (template.footer.showPageNumbers) {
            doc.text('Page 1 of 1', 105, 287, { align: 'center' });
        }
    }

    function hexToRgb(hex) {
        // Convert hex color to RGB
        hex = hex.replace(/^#/, '');

        // Handle shorthand hex
        if (hex.length === 3) {
            hex = hex[0] + hex[0] + hex[1] + hex[1] + hex[2] + hex[2];
        }

        const r = parseInt(hex.substring(0, 2), 16);
        const g = parseInt(hex.substring(2, 4), 16);
        const b = parseInt(hex.substring(4, 6), 16);

        return { r, g, b };
    }

    function generateInvoiceFilename(invoiceData) {
        // Get invoice number (or use 'New' if not available)
        const invoiceNumber = invoiceData.invoiceNumber || 'New';

        // Format dates for filename
        let dateRange = '';

        if (invoiceData.invoiceDate && invoiceData.dueDate) {
            // Format dates to YYYY-MM-DD format for the filename
            const invoiceDate = formatDateForFilename(invoiceData.invoiceDate);
            const dueDate = formatDateForFilename(invoiceData.dueDate);
            dateRange = `_${invoiceDate}_to_${dueDate}`;
        } else if (invoiceData.invoiceDate) {
            // If only invoice date is available
            const invoiceDate = formatDateForFilename(invoiceData.invoiceDate);
            dateRange = `_${invoiceDate}`;
        }

        // Clean the invoice number to make it filename-safe
        const safeInvoiceNumber = invoiceNumber.replace(/[\\/:*?"<>|]/g, '-');

        // Generate the filename
        return `Invoice_${safeInvoiceNumber}${dateRange}.pdf`;
    }

    function formatDateForFilename(dateString) {
        // Check if the date is in YYYY-MM-DD format
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
            return dateString; // Already in the correct format
        }

        // Try to parse the date
        try {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return ''; // Invalid date
            }

            // Format as YYYY-MM-DD
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');

            return `${year}-${month}-${day}`;
        } catch (e) {
            console.error('Error formatting date:', e);
            return ''; // Return empty string on error
        }
    }

    function exportToExcel() {
        try {
            // Get invoice data
            const invoiceData = getInvoiceData();

            // Create workbook
            const wb = XLSX.utils.book_new();

            // Get the selected template
            const templateName = settings.selectedTemplate || 'classic';
            const template = templates[templateName] || templates.classic;

            // Get template colors for styling
            const primaryColor = templateName === 'classic' ? '2980b9' :
                               templateName === 'modern' ? '3498db' :
                               templateName === 'minimal' ? '333333' :
                               templateName === 'creative' ? '9b59b6' : '2980b9';

            const secondaryColor = templateName === 'classic' ? 'f8f9fa' :
                                 templateName === 'modern' ? 'e8f4fc' :
                                 templateName === 'minimal' ? 'f5f5f5' :
                                 templateName === 'creative' ? 'f3e5f5' : 'f8f9fa';

            // Create invoice summary worksheet with better formatting
            const summaryData = [
                ['INVOICE', '', '', ''],
                [''],
                ['Invoice Number:', invoiceData.invoiceNumber, '', ''],
                ['Invoice Date:', invoiceData.invoiceDate, '', ''],
                ['Due Date:', invoiceData.dueDate, '', ''],
                [''],
                ['FROM:', '', '', ''],
                [invoiceData.business.name, '', '', ''],
                [invoiceData.business.address, '', '', ''],
                [invoiceData.business.phone, '', '', ''],
                [invoiceData.business.email, '', '', ''],
                [''],
                ['TO:', '', '', ''],
                [invoiceData.client.name, '', '', ''],
                [invoiceData.client.address, '', '', ''],
                [invoiceData.client.phone, '', '', ''],
                [invoiceData.client.email, '', '', ''],
                [''],
                ['SUMMARY', '', '', ''],
                [''],
                ['Subtotal:', '', '', `${invoiceData.currencySymbol}${invoiceData.subtotal.toFixed(2)}`],
                ['Tax:', '', '', `${invoiceData.currencySymbol}${invoiceData.taxTotal.toFixed(2)}`],
                [`Total ${invoiceData.currency}:`, '', '', `${invoiceData.currencySymbol}${invoiceData.grandTotal.toFixed(2)}`],
                [''],
                [invoiceData.notesTitle || 'Notes:', '', '', ''],
                [invoiceData.notes || '', '', '', '']
            ];

            // Create the worksheet
            const summaryWs = XLSX.utils.aoa_to_sheet(summaryData);

            // Apply styling to the summary worksheet
            const summaryRange = XLSX.utils.decode_range(summaryWs['!ref']);

            // Set column widths
            summaryWs['!cols'] = [
                { wch: 25 }, // A column - labels
                { wch: 20 }, // B column
                { wch: 15 }, // C column
                { wch: 20 }  // D column - values
            ];

            // Create a style object for the worksheet
            summaryWs['!styles'] = {};

            // Style the header
            summaryWs['A1'] = { v: 'INVOICE', t: 's', s: { font: { bold: true, sz: 16, color: { rgb: primaryColor } } } };

            // Style section headers
            ['A7', 'A13', 'A19'].forEach(cell => {
                if (summaryWs[cell]) {
                    summaryWs[cell].s = { font: { bold: true, color: { rgb: primaryColor } } };
                }
            });

            // Style the business and client names
            ['A8', 'A14'].forEach(cell => {
                if (summaryWs[cell]) {
                    summaryWs[cell].s = { font: { bold: true } };
                }
            });

            // Style the summary section
            for (let row = 20; row <= 22; row++) {
                // Style the labels
                if (summaryWs[`A${row}`]) {
                    summaryWs[`A${row}`].s = { font: { bold: true } };
                }

                // Style the values
                if (summaryWs[`D${row}`]) {
                    summaryWs[`D${row}`].s = {
                        font: { bold: row === 22 }, // Bold for total
                        alignment: { horizontal: settings.summaryAlignment || 'right' }
                    };
                }
            }

            // Style the total row with the primary color
            if (summaryWs['A22']) {
                summaryWs['A22'].s = { font: { bold: true, color: { rgb: primaryColor } } };
            }
            if (summaryWs['D22']) {
                summaryWs['D22'].s = { font: { bold: true, color: { rgb: primaryColor } } };
            }

            // Add the summary worksheet to the workbook
            XLSX.utils.book_append_sheet(wb, summaryWs, 'Invoice Summary');

            // Create items worksheet with better formatting
            const itemsHeaders = ['Description', 'Quantity', 'Unit Price', 'Tax Rate (%)', 'Tax Amount', 'Total'];
            const itemsData = [itemsHeaders];

            invoiceData.items.forEach(item => {
                itemsData.push([
                    item.description,
                    item.quantity,
                    `${invoiceData.currencySymbol}${item.price.toFixed(2)}`,
                    `${item.taxRate}%`,
                    `${invoiceData.currencySymbol}${item.tax.toFixed(2)}`,
                    `${invoiceData.currencySymbol}${item.amount.toFixed(2)}`
                ]);
            });

            // Add a total row at the bottom
            itemsData.push([
                '', '', '', '', 'Total:',
                `${invoiceData.currencySymbol}${invoiceData.grandTotal.toFixed(2)}`
            ]);

            // Create the items worksheet
            const itemsWs = XLSX.utils.aoa_to_sheet(itemsData);

            // Set column widths for items worksheet
            itemsWs['!cols'] = [
                { wch: 40 }, // Description
                { wch: 10 }, // Quantity
                { wch: 15 }, // Unit Price
                { wch: 12 }, // Tax Rate
                { wch: 15 }, // Tax Amount
                { wch: 15 }  // Total
            ];

            // Style the header row
            const itemsRange = XLSX.utils.decode_range(itemsWs['!ref']);
            for (let col = itemsRange.s.c; col <= itemsRange.e.c; col++) {
                const cellRef = XLSX.utils.encode_cell({ r: 0, c: col });
                itemsWs[cellRef].s = {
                    fill: { fgColor: { rgb: secondaryColor } },
                    font: { bold: true, color: { rgb: '000000' } },
                    border: {
                        top: { style: 'thin', color: { rgb: 'CCCCCC' } },
                        bottom: { style: 'thin', color: { rgb: 'CCCCCC' } }
                    }
                };
            }

            // Style the total row
            const totalRowIndex = itemsData.length - 1;
            for (let col = itemsRange.s.c; col <= itemsRange.e.c; col++) {
                const cellRef = XLSX.utils.encode_cell({ r: totalRowIndex, c: col });
                if (itemsWs[cellRef]) {
                    itemsWs[cellRef].s = {
                        font: { bold: true, color: col >= 4 ? { rgb: primaryColor } : { rgb: '000000' } }
                    };
                }
            }

            // Add the items worksheet to the workbook
            XLSX.utils.book_append_sheet(wb, itemsWs, 'Invoice Items');

            // Generate filename with invoice number and date range (same as PDF but with .xlsx extension)
            const filename = generateInvoiceFilename(invoiceData).replace('.pdf', '.xlsx');

            // Save the Excel file
            XLSX.writeFile(wb, filename);

            setStatus('Excel file exported successfully.', 'success');
        } catch (error) {
            console.error('Excel Export Error:', error);
            setStatus(`Error exporting Excel: ${error.message}`, 'error');
        }
    }

    function setTheme(themeName) {
        if (themeName === 'default') {
            document.body.removeAttribute('data-theme');
        } else {
            document.body.setAttribute('data-theme', themeName);
        }
        // Theme is now managed by the parent dashboard
    }

    function initSummarySpacingSlider() {
        const summarySpacingSlider = document.getElementById('summary-spacing');
        const resetSpacingBtn = document.getElementById('reset-spacing-btn');

        if (summarySpacingSlider) {
            // Update the displayed value when the slider changes
            summarySpacingSlider.addEventListener('input', function() {
                const sliderValue = this.nextElementSibling;
                if (sliderValue) {
                    sliderValue.textContent = this.value;
                }
            });
        }

        if (resetSpacingBtn) {
            // Reset the slider to default value when the reset button is clicked
            resetSpacingBtn.addEventListener('click', function() {
                const defaultSpacing = 50; // Default spacing value
                if (summarySpacingSlider) {
                    summarySpacingSlider.value = defaultSpacing;

                    // Update the displayed value
                    const sliderValue = summarySpacingSlider.nextElementSibling;
                    if (sliderValue) {
                        sliderValue.textContent = defaultSpacing;
                    }
                }
            });
        }
    }

    function setStatus(message, type) {
        // Clear any existing timeout
        if (window.statusTimeout) {
            clearTimeout(window.statusTimeout);
        }

        // Create or update the status area
        if (!statusArea) return;

        // Set the message and styling
        statusArea.textContent = message;
        statusArea.className = '';
        statusArea.classList.add(type);

        // Show the notification
        statusArea.style.display = 'block';

        // Use setTimeout to add the 'show' class in the next frame for animation
        setTimeout(() => {
            statusArea.classList.add('show');
        }, 10);

        // Auto-hide after 5 seconds
        window.statusTimeout = setTimeout(() => {
            // Start the fade-out animation
            statusArea.classList.remove('show');

            // Wait for the animation to complete before hiding
            setTimeout(() => {
                statusArea.style.display = 'none';
                statusArea.className = '';
            }, 300); // Match the transition duration in CSS
        }, 5000);
    }

    function loadSettings() {
        const savedSettings = localStorage.getItem('invoice-maker-settings');
        if (savedSettings) {
            try {
                const settings = JSON.parse(savedSettings);

                // Check if this is an old version without the PHP/0% defaults
                // If defaultCurrency is USD and defaultTaxRate is 10, update to new defaults
                if (settings.defaultCurrency === 'USD' && settings.defaultTaxRate === 10) {
                    settings.defaultCurrency = 'PHP';
                    settings.defaultTaxRate = 0;
                    // Save the updated settings
                    saveSettings(settings);
                }

                return settings;
            } catch (e) {
                console.error('Error parsing saved settings:', e);
                return getDefaultSettings();
            }
        }
        return getDefaultSettings();
    }

    function saveSettings(settings) {
        localStorage.setItem('invoice-maker-settings', JSON.stringify(settings));
    }

    function getDefaultSettings() {
        return {
            defaultCurrency: 'PHP',
            defaultTaxRate: 0,
            pdfPageSize: 'a4',
            pdfOrientation: 'portrait',
            selectedTemplate: 'classic',
            summarySpacing: 50, // Default spacing for summary section (mm)
            summaryAlignment: 'right' // Default alignment for summary amounts (left or right)
        };
    }

    // Preset Management Functions
    function initializePresets() {
        // Create default Hastings preset if it doesn't exist
        createDefaultPresets();

        // Load presets into dropdown
        loadPresetsIntoDropdown();

        // Update save button state
        updateSaveButtonState();
    }

    function createDefaultPresets() {
        const presets = getPresets();

        // Create Hastings preset if it doesn't exist
        if (!presets.find(p => p.name === 'Hastings')) {
            const hastingsPreset = {
                name: 'Hastings',
                data: {
                    invoiceNumber: 'INV-001',
                    invoiceDate: '',
                    dueDate: '',
                    business: {
                        name: 'Kaithnyca Gonzaga',
                        address: 'Cebu City, Philippines, 6000',
                        phone: '+63 ************',
                        email: '<EMAIL>',
                        website: ''
                    },
                    client: {
                        name: 'Cloud Admin Office',
                        address: '22 Greenhill Road, Wayville, SA 5034, Australia',
                        phone: '',
                        email: '<EMAIL>'
                    },
                    items: [{
                        description: 'Accounting Services May 2025',
                        quantity: 20,
                        price: 60000.00,
                        taxRate: 0
                    }],
                    notesTitle: 'Bank Details:',
                    notes: 'Bank Name: Bank of the Philippine Islands\nAccount Name: Kaithnyca Gonzaga\nAccount Number: **********\nSwift code: BOPIPHMM\nPayment terms: 7 days from invoice date'
                }
            };

            presets.push(hastingsPreset);
            savePresets(presets);
        }
    }

    function getPresets() {
        const savedPresets = localStorage.getItem('invoice-maker-presets');
        if (savedPresets) {
            try {
                return JSON.parse(savedPresets);
            } catch (e) {
                console.error('Error parsing saved presets:', e);
                return [];
            }
        }
        return [];
    }

    function savePresets(presets) {
        localStorage.setItem('invoice-maker-presets', JSON.stringify(presets));
    }

    function loadPresetsIntoDropdown() {
        const presets = getPresets();
        presetSelector.innerHTML = '<option value="">Select a preset...</option>';

        presets.forEach(preset => {
            const option = document.createElement('option');
            option.value = preset.name;
            option.textContent = preset.name;
            presetSelector.appendChild(option);
        });
    }

    function loadSelectedPreset() {
        const selectedPresetName = presetSelector.value;
        if (!selectedPresetName) {
            deletePresetBtn.disabled = true;
            presetNameInput.value = '';
            return;
        }

        const presets = getPresets();
        const preset = presets.find(p => p.name === selectedPresetName);

        if (preset) {
            loadPresetIntoForm(preset.data);
            presetNameInput.value = preset.name;
            deletePresetBtn.disabled = false;
            setStatus(`Preset "${preset.name}" loaded successfully.`, 'success');
        }
    }

    function loadPresetIntoForm(data) {
        // Load invoice information
        document.getElementById('invoice-number').value = data.invoiceNumber || '';
        document.getElementById('invoice-date').value = data.invoiceDate || '';
        document.getElementById('due-date').value = data.dueDate || '';

        // Load business information
        document.getElementById('business-name').value = data.business.name || '';
        document.getElementById('business-address').value = data.business.address || '';
        document.getElementById('business-phone').value = data.business.phone || '';
        document.getElementById('business-email').value = data.business.email || '';
        document.getElementById('business-website').value = data.business.website || '';

        // Load client information
        document.getElementById('client-name').value = data.client.name || '';
        document.getElementById('client-address').value = data.client.address || '';
        document.getElementById('client-phone').value = data.client.phone || '';
        document.getElementById('client-email').value = data.client.email || '';

        // Load notes
        document.getElementById('notes-title').value = data.notesTitle || 'Notes';
        document.getElementById('invoice-notes').value = data.notes || '';

        // Clear existing items and load preset items
        const itemsBody = document.getElementById('invoice-items-body');
        itemsBody.innerHTML = '';
        itemCounter = 0;

        if (data.items && data.items.length > 0) {
            data.items.forEach(item => {
                addInvoiceItem();
                const currentRow = itemsBody.lastElementChild;
                currentRow.querySelector('.item-description').value = item.description || '';
                currentRow.querySelector('.item-quantity').value = item.quantity || 1;
                currentRow.querySelector('.item-price').value = item.price || 0;
                currentRow.querySelector('.item-tax').value = item.taxRate || 0;

                // Trigger calculation
                calculateItemAmount(currentRow);
            });
        } else {
            // Add one empty item if no items in preset
            addInvoiceItem();
        }

        // Update totals
        updateTotals();
    }

    function getCurrentFormData() {
        return {
            invoiceNumber: document.getElementById('invoice-number').value,
            invoiceDate: document.getElementById('invoice-date').value,
            dueDate: document.getElementById('due-date').value,
            business: {
                name: document.getElementById('business-name').value,
                address: document.getElementById('business-address').value,
                phone: document.getElementById('business-phone').value,
                email: document.getElementById('business-email').value,
                website: document.getElementById('business-website').value
            },
            client: {
                name: document.getElementById('client-name').value,
                address: document.getElementById('client-address').value,
                phone: document.getElementById('client-phone').value,
                email: document.getElementById('client-email').value
            },
            items: getInvoiceItems(),
            notesTitle: document.getElementById('notes-title').value,
            notes: document.getElementById('invoice-notes').value
        };
    }

    function getInvoiceItems() {
        const items = [];
        const rows = document.querySelectorAll('#invoice-items-body tr');

        rows.forEach(row => {
            const description = row.querySelector('.item-description').value;
            const quantity = parseFloat(row.querySelector('.item-quantity').value) || 0;
            const price = parseFloat(row.querySelector('.item-price').value) || 0;
            const taxRate = parseFloat(row.querySelector('.item-tax').value) || 0;

            if (description || quantity || price) {
                items.push({
                    description,
                    quantity,
                    price,
                    taxRate
                });
            }
        });

        return items;
    }

    function saveCurrentPreset() {
        const presetName = presetNameInput.value.trim();
        if (!presetName) {
            setStatus('Please enter a preset name.', 'error');
            return;
        }

        const formData = getCurrentFormData();
        const presets = getPresets();

        // Check if preset already exists
        const existingIndex = presets.findIndex(p => p.name === presetName);

        if (existingIndex >= 0) {
            // Update existing preset
            presets[existingIndex].data = formData;
            setStatus(`Preset "${presetName}" updated successfully.`, 'success');
        } else {
            // Create new preset
            presets.push({
                name: presetName,
                data: formData
            });
            setStatus(`Preset "${presetName}" saved successfully.`, 'success');
        }

        savePresets(presets);
        loadPresetsIntoDropdown();

        // Select the saved preset in dropdown
        presetSelector.value = presetName;
        deletePresetBtn.disabled = false;
    }

    function deleteSelectedPreset() {
        const selectedPresetName = presetSelector.value;
        if (!selectedPresetName) {
            return;
        }

        if (confirm(`Are you sure you want to delete the preset "${selectedPresetName}"?`)) {
            const presets = getPresets();
            const filteredPresets = presets.filter(p => p.name !== selectedPresetName);

            savePresets(filteredPresets);
            loadPresetsIntoDropdown();

            presetSelector.value = '';
            presetNameInput.value = '';
            deletePresetBtn.disabled = true;

            setStatus(`Preset "${selectedPresetName}" deleted successfully.`, 'success');
        }
    }

    function autoSaveCurrentPreset() {
        const selectedPresetName = presetSelector.value;
        if (selectedPresetName) {
            const formData = getCurrentFormData();
            const presets = getPresets();
            const existingIndex = presets.findIndex(p => p.name === selectedPresetName);

            if (existingIndex >= 0) {
                presets[existingIndex].data = formData;
                savePresets(presets);
                setStatus(`Preset "${selectedPresetName}" auto-updated from current form data.`, 'info');
            }
        }
    }

    function updateSaveButtonState() {
        const presetName = presetNameInput.value.trim();
        savePresetBtn.disabled = !presetName;
    }

    // Theme is now managed by the parent dashboard
});
