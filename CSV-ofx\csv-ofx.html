<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web OFX Converter (MYOB Style)</title>
    <!-- PapaParse for CSV Handling (via CDN) -->
    <script src="papaparse.min.js"></script>
    <style>
        /* Theme Colors - Default Light Blue */
        :root {
            /* Light Blue Theme (Default) */
            --primary-color: #3498db;       /* Main blue */
            --primary-dark: #2980b9;        /* Darker blue */
            --secondary-color: #2ecc71;     /* Green */
            --secondary-dark: #27ae60;      /* Darker green */
            --accent-color: #e74c3c;        /* Red accent */
            --accent-dark: #c0392b;         /* Darker red */
            --neutral-light: #f8f9fa;       /* Light background */
            --neutral-medium: #e9ecef;      /* Medium background */
            --neutral-dark: #343a40;        /* Dark text */
            --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
            --bg-color: #ffffff;            /* Background color */
            --text-color: #333333;          /* Text color */
        }

        /* Dark Theme */
        [data-theme="dark"] {
            --primary-color: #4dabf7;       /* Brighter blue for dark mode */
            --primary-dark: #339af0;        /* Slightly lighter blue */
            --secondary-color: #51cf66;     /* Brighter green */
            --secondary-dark: #40c057;      /* Slightly lighter green */
            --accent-color: #ff6b6b;        /* Brighter red */
            --accent-dark: #fa5252;         /* Slightly lighter red */
            --neutral-light: #343a40;       /* Dark background */
            --neutral-medium: #495057;      /* Medium dark background */
            --neutral-dark: #dee2e6;        /* Light text */
            --shadow: rgba(0, 0, 0, 0.3);   /* Darker shadow */
            --bg-color: #212529;            /* Dark background */
            --text-color: #f8f9fa;          /* Light text */
        }

        /* Purple Theme */
        [data-theme="purple"] {
            --primary-color: #9775fa;       /* Main purple */
            --primary-dark: #845ef7;        /* Darker purple */
            --secondary-color: #5c7cfa;     /* Blue secondary */
            --secondary-dark: #4c6ef5;      /* Darker blue */
            --accent-color: #ff8787;        /* Coral accent */
            --accent-dark: #ff6b6b;         /* Darker coral */
            --neutral-light: #f8f9fa;       /* Light background */
            --neutral-medium: #e9ecef;      /* Medium background */
            --neutral-dark: #343a40;        /* Dark text */
            --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
            --bg-color: #ffffff;            /* Background color */
            --text-color: #333333;          /* Text color */
        }

        /* Green Theme */
        [data-theme="green"] {
            --primary-color: #40c057;       /* Main green */
            --primary-dark: #37b24d;        /* Darker green */
            --secondary-color: #4dabf7;     /* Blue secondary */
            --secondary-dark: #339af0;      /* Darker blue */
            --accent-color: #ff922b;        /* Orange accent */
            --accent-dark: #fd7e14;         /* Darker orange */
            --neutral-light: #f8f9fa;       /* Light background */
            --neutral-medium: #e9ecef;      /* Medium background */
            --neutral-dark: #343a40;        /* Dark text */
            --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
            --bg-color: #ffffff;            /* Background color */
            --text-color: #333333;          /* Text color */
        }

        /* Dark Blue Theme */
        [data-theme="dark-blue"] {
            --primary-color: #228be6;       /* Main blue */
            --primary-dark: #1c7ed6;        /* Darker blue */
            --secondary-color: #15aabf;     /* Teal secondary */
            --secondary-dark: #1098ad;      /* Darker teal */
            --accent-color: #f76707;        /* Orange accent */
            --accent-dark: #e8590c;         /* Darker orange */
            --neutral-light: #343a40;       /* Dark background */
            --neutral-medium: #495057;      /* Medium dark background */
            --neutral-dark: #dee2e6;        /* Light text */
            --shadow: rgba(0, 0, 0, 0.3);   /* Darker shadow */
            --bg-color: #1e2a3a;            /* Dark blue background */
            --text-color: #f8f9fa;          /* Light text */
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 20px;
            background-color: var(--bg-color);
            color: var(--text-color);
            transition: all 0.3s ease;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: var(--bg-color);
            padding: 25px;
            border-radius: 10px;
            box-shadow: 0 4px 15px var(--shadow);
        }
        h1, h2 {
            color: var(--primary-color);
            border-bottom: 2px solid var(--neutral-medium);
            padding-bottom: 8px;
            margin-top: 1.5em;
            font-weight: 600;
        }
        h1:first-child, h2:first-child {
             margin-top: 0;
        }

        /* Configuration Section */
        #config-form {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid var(--neutral-medium);
            border-radius: 8px;
            background-color: #fdfdfd;
            box-shadow: 0 2px 8px var(--shadow);
        }
        #config-form label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--primary-dark);
        }
        #config-form input[type="text"],
        #config-form select {
            width: 100%;
            padding: 10px;
            border: 1px solid var(--neutral-medium);
            border-radius: 6px;
            box-sizing: border-box; /* Include padding in width */
            transition: border-color 0.2s ease;
        }
        #config-form input[type="text"]:focus,
        #config-form select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        #config-form input:required,
        #config-form select:required {
             border-left: 3px solid var(--accent-color); /* Indicate required */
        }

        /* Table Styling */
        #transaction-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin-top: 15px;
            font-size: 0.95em;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px var(--shadow);
        }
        #transaction-table th,
        #transaction-table td {
            border: 1px solid var(--neutral-medium);
            padding: 10px 12px;
            text-align: left;
            vertical-align: top; /* Align content top */
        }
        #transaction-table thead {
            background-color: var(--primary-color);
            color: white;
            font-weight: bold;
        }
        #transaction-table thead th {
            border-color: var(--primary-dark);
        }
        #transaction-table tbody tr:nth-child(odd) {
            background-color: var(--neutral-light);
        }
        #transaction-table tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.05);
        }
        /* Inputs within table */
        #transaction-table input[type="date"],
        #transaction-table input[type="text"],
        #transaction-table input[type="number"],
        #transaction-table select {
            width: 100%;
            padding: 8px 10px;
            border: 1px solid var(--neutral-medium);
            border-radius: 4px;
            box-sizing: border-box;
            font-size: inherit; /* Match table font size */
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }
        #transaction-table input:focus,
        #transaction-table select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
        }
        #transaction-table input[type="number"] {
            text-align: right;
        }
        #transaction-table .action-cell {
            text-align: center;
            white-space: nowrap;
        }
        #transaction-table .delete-row-btn {
            background-color: var(--accent-color);
            color: white;
            border: none;
            padding: 6px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.9em;
            transition: background-color 0.2s ease;
        }
        #transaction-table .delete-row-btn:hover {
            background-color: var(--accent-dark);
        }

        /* Buttons */
        .button-bar {
            margin-top: 25px;
            padding-top: 20px;
            border-top: 1px solid var(--neutral-medium);
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            justify-content: flex-start;
        }
        .button-bar button, .file-upload-label {
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.2s ease;
            box-shadow: 0 2px 5px var(--shadow);
        }
        .button-bar button:active, .file-upload-label:active {
            transform: translateY(1px);
            box-shadow: 0 1px 2px var(--shadow);
        }
        .button-bar button.primary {
            background-color: var(--primary-color);
            color: white;
        }
        .button-bar button.primary:hover {
            background-color: var(--primary-dark);
        }
        .button-bar button.secondary {
            background-color: var(--neutral-dark);
            color: white;
        }
        .button-bar button.secondary:hover {
            background-color: #23272b;
        }
        .file-upload-label {
            background-color: var(--secondary-color);
            color: white;
            display: inline-block; /* Align with buttons */
        }
        .file-upload-label:hover {
            background-color: var(--secondary-dark);
        }
        #csv-file-input {
            display: none; /* Hide actual file input */
        }

        /* Status Area */
        #status-area {
            margin-top: 20px;
            padding: 15px;
            background-color: var(--neutral-medium);
            border: 1px solid #ced4da;
            border-radius: 6px;
            color: var(--neutral-dark);
            min-height: 1.6em; /* Prevent layout jump */
            box-shadow: 0 2px 5px var(--shadow);
            transition: all 0.3s ease;
        }
        #status-area.error {
            background-color: rgba(231, 76, 60, 0.15);
            color: var(--accent-dark);
            border-color: var(--accent-color);
        }
        #status-area.success {
            background-color: rgba(46, 204, 113, 0.15);
            color: var(--secondary-dark);
            border-color: var(--secondary-color);
        }

        /* Required marker */
        label .required-star, #transaction-table th .required-star {
            color: var(--accent-color);
            margin-left: 3px;
            font-weight: bold;
        }

        /* Theme Selector - Removed from individual tools */

    </style>
</head>
<body>
    <div class="container">
        <!-- Theme selector removed - now controlled by dashboard -->
        <h1>OFX Converter (Web App)</h1>
        <p>Enter transaction details below or load from CSV. Click 'Convert to OFX' to generate and download the file.</p>

        <!-- OFX Configuration -->
        <h2>OFX Account Configuration</h2>
        <form id="config-form">
            <div>
                <label for="bank-id">Bank ID (BSB/Routing):<span class="required-star">*</span></label>
                <input type="text" id="bank-id" required>
            </div>
            <div>
                <label for="account-id">Account ID:<span class="required-star">*</span></label>
                <input type="text" id="account-id" required>
            </div>
            <div>
                <label for="account-type">Account Type:<span class="required-star">*</span></label>
                <select id="account-type" required>
                    <option value="CHECKING" selected>Checking</option>
                    <option value="SAVINGS">Savings</option>
                    <option value="MONEYMRKT">Money Market</option>
                    <option value="CREDITLINE">Credit Line</option>
                    <option value="CREDITCARD">Credit Card</option>
                </select>
            </div>
            <div>
                <label for="currency">Currency (e.g., AUD):<span class="required-star">*</span></label>
                <input type="text" id="currency" value="AUD" required>
            </div>
             <div>
                <label for="org">Bank Name (ORG):</label>
                <input type="text" id="org" placeholder="Your Bank Name">
            </div>
             <div>
                <label for="fid">FI ID (FID, Optional):</label>
                <input type="text" id="fid" placeholder="Optional Financial Institution ID">
            </div>
        </form>

        <!-- Transaction Entry Table -->
        <h2>Transaction Entry</h2>
        <div id="transaction-table-container">
            <table id="transaction-table">
                <thead>
                    <tr>
                        <!-- Add required markers to headers -->
                        <th>Date<span class="required-star">*</span></th>
                        <th>Category<span class="required-star">*</span></th>
                        <th>Amount (+)<span class="required-star">*</span></th>
                        <th>Tax Code<span class="required-star">*</span></th>
                        <th>Payee</th>
                        <th>Reference</th>
                        <th>Description</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Rows will be added by JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Buttons -->
        <div class="button-bar">
            <button id="add-row-btn" class="secondary">Add Row</button>
            <input type="file" id="csv-file-input" accept=".csv">
            <label for="csv-file-input" class="file-upload-label secondary">Load CSV</label>
            <button id="save-csv-btn" class="secondary">Save as CSV</button>
            <button id="convert-ofx-btn" class="primary">Convert Table to OFX</button>
        </div>

        <!-- Status Area -->
        <div id="status-area">Status messages will appear here...</div>

    </div> <!-- /container -->

    <script>
        // --- !!! EDIT REQUIRED !!! ---
        // --- Populate these arrays with your actual MYOB data ---
        const chartOfAccounts = [
            // Format: "Code - Name (Type)" - Must match exactly for selection later if needed
            // Add ALL relevant accounts from your MYOB chart
            "1-1110 ANZ Transaction account (ASSET)",
            "1-1400 St G Business Visa (CREDIT_CARD)",
            "2-1200 Accounts Payable (ACCOUNTS_PAYABLE)",
            "2-1400 GST Liabilities (LIABILITY)",
            "6-2100 Accounting Fees (EXPENSE)",
            "6-2120 Advertising (EXPENSE)",
            "6-2140 Bank Charges (EXPENSE)",
            "6-4100 Office Supplies (EXPENSE)",
            "6-5410 Telephone & internet (EXPENSE)",
            // ... ADD MANY MORE HERE ...
        ].sort(); // Keep sorted

        const taxCodes = [
             // Just the code needed here
            "ABN", "CAP", "EXP", "FRE", "GNR", "GST", "INP", "ITS", "N-T",
            // ... ADD ANY OTHERS HERE ...
        ].sort(); // Keep sorted
        // --- End of EDIT REQUIRED section ---


        // --- Global State ---
        let transactions = []; // Array of row data objects { _row_id, Date, Category, ... }
        let rowCounter = 0;

        // --- DOM References ---
        const tableBody = document.getElementById('transaction-table').querySelector('tbody');
        const addRowBtn = document.getElementById('add-row-btn');
        const convertOfxBtn = document.getElementById('convert-ofx-btn');
        const configForm = document.getElementById('config-form');
        const statusArea = document.getElementById('status-area');
        const csvFileInput = document.getElementById('csv-file-input');
        const saveCsvBtn = document.getElementById('save-csv-btn');
        // Config Inputs
        const bankIdInput = document.getElementById('bank-id');
        const accountIdInput = document.getElementById('account-id');
        const accountTypeSelect = document.getElementById('account-type');
        const currencyInput = document.getElementById('currency');
        const orgInput = document.getElementById('org');
        const fidInput = document.getElementById('fid');


        // --- Utility Functions ---
        function setStatus(message, type = 'info') { // type: 'info', 'success', 'error'
            statusArea.textContent = message;
            statusArea.className = type; // Set class for styling
        }

        function populateSelect(selectElement, optionsArray, selectedValue = '', addBlankOption = true) {
            selectElement.innerHTML = ''; // Clear existing options
            if (addBlankOption) {
                const blankOpt = document.createElement('option');
                blankOpt.value = '';
                blankOpt.textContent = '-- Select --';
                selectElement.appendChild(blankOpt);
            }
            optionsArray.forEach(optionValue => {
                const opt = document.createElement('option');
                opt.value = optionValue;
                opt.textContent = optionValue; // Display the same as value for now
                selectElement.appendChild(opt);
            });
            selectElement.value = selectedValue; // Set the initial selection
        }

        // --- Core Table Rendering ---
        function renderTable() {
            tableBody.innerHTML = ''; // Clear table
            if (transactions.length === 0) {
                // Optional: Show a message row if table is empty
                const tr = tableBody.insertRow();
                const td = tr.insertCell();
                td.colSpan = document.getElementById('transaction-table').rows[0].cells.length; // Match column count
                td.textContent = "No transactions added yet. Click 'Add Row' or 'Load CSV'.";
                td.style.textAlign = 'center';
                td.style.fontStyle = 'italic';
                td.style.color = '#888';
            } else {
                transactions.forEach(rowData => {
                    const tr = tableBody.insertRow();
                    tr.setAttribute('data-row-id', rowData._row_id);

                    // 1. Date Cell
                    let td = tr.insertCell();
                    const dateInput = document.createElement('input');
                    dateInput.type = 'date';
                    dateInput.value = rowData.Date || ''; // Expects YYYY-MM-DD
                    dateInput.required = true;
                    dateInput.addEventListener('change', (e) => updateTransactionData(rowData._row_id, 'Date', e.target.value));
                    td.appendChild(dateInput);

                    // 2. Category Cell
                    td = tr.insertCell();
                    const categorySelect = document.createElement('select');
                    categorySelect.required = true;
                    populateSelect(categorySelect, chartOfAccounts, rowData.Category);
                    categorySelect.addEventListener('change', (e) => updateTransactionData(rowData._row_id, 'Category', e.target.value));
                    td.appendChild(categorySelect);

                    // 3. Amount Cell
                    td = tr.insertCell();
                    const amountInput = document.createElement('input');
                    amountInput.type = 'number';
                    amountInput.step = '0.01';
                    amountInput.min = '0'; // Enforce positive input visually
                    amountInput.value = rowData.Amount !== undefined && rowData.Amount !== null ? Math.abs(rowData.Amount).toFixed(2) : ''; // Display positive
                    amountInput.required = true;
                    amountInput.placeholder = 'Enter positive amount';
                    amountInput.addEventListener('change', (e) => {
                        const val = parseFloat(e.target.value || 0);
                        updateTransactionData(rowData._row_id, 'Amount', Math.abs(val)); // Store positive
                    });
                    td.appendChild(amountInput);

                    // 4. Tax Code Cell
                    td = tr.insertCell();
                    const taxSelect = document.createElement('select');
                    taxSelect.required = true;
                    populateSelect(taxSelect, taxCodes, rowData.TaxCode);
                    taxSelect.addEventListener('change', (e) => updateTransactionData(rowData._row_id, 'TaxCode', e.target.value));
                    td.appendChild(taxSelect);

                    // 5. Payee Cell
                    td = tr.insertCell();
                    const payeeInput = document.createElement('input');
                    payeeInput.type = 'text';
                    payeeInput.value = rowData.Payee || '';
                    payeeInput.addEventListener('change', (e) => updateTransactionData(rowData._row_id, 'Payee', e.target.value));
                    td.appendChild(payeeInput);

                    // 6. Reference Cell
                    td = tr.insertCell();
                    const refInput = document.createElement('input');
                    refInput.type = 'text';
                    refInput.value = rowData.Reference || '';
                    refInput.addEventListener('change', (e) => updateTransactionData(rowData._row_id, 'Reference', e.target.value));
                    td.appendChild(refInput);

                    // 7. Description Cell
                    td = tr.insertCell();
                    const descInput = document.createElement('input');
                    descInput.type = 'text';
                    descInput.value = rowData.Description || '';
                    descInput.addEventListener('change', (e) => updateTransactionData(rowData._row_id, 'Description', e.target.value));
                    td.appendChild(descInput);

                    // 8. Actions Cell
                    td = tr.insertCell();
                    td.classList.add('action-cell');
                    const deleteBtn = document.createElement('button');
                    deleteBtn.textContent = 'Delete';
                    deleteBtn.classList.add('delete-row-btn');
                    deleteBtn.onclick = () => deleteRow(rowData._row_id);
                    td.appendChild(deleteBtn);
                });
            }
        }

        // --- Data Handling Functions ---
        function updateTransactionData(rowId, field, value) {
            const index = transactions.findIndex(row => row._row_id === rowId);
            if (index !== -1) {
                transactions[index][field] = value;
                // console.log(`Updated row ${rowId}, field ${field}:`, value); // Optional debug log
            } else {
                console.error("Could not find row with ID:", rowId, "to update field:", field);
            }
        }

        function addRow() {
            rowCounter++;
            const today = new Date();
            const yyyy = today.getFullYear();
            const mm = String(today.getMonth() + 1).padStart(2, '0'); // Month is 0-indexed
            const dd = String(today.getDate()).padStart(2, '0');

            const newRowData = {
                _row_id: rowCounter,
                Date: `${yyyy}-${mm}-${dd}`, // Default date YYYY-MM-DD
                Category: '', Amount: null, TaxCode: '', // Use null/undefined for amount initially?
                Payee: '', Reference: '', Description: ''
            };
            transactions.push(newRowData);
            renderTable(); // Re-render the whole table
            // Optional: scroll to the new row
            const newRowElement = tableBody.querySelector(`tr[data-row-id="${rowCounter}"]`);
            if (newRowElement) {
                 newRowElement.scrollIntoView({ behavior: "smooth", block: "nearest" });
                 // Focus first editable element?
                 const firstInput = newRowElement.querySelector('input, select');
                 if(firstInput) firstInput.focus();
            }
            setStatus('Added new row.', 'info');
        }

        function deleteRow(rowId) {
            if (confirm(`Are you sure you want to delete row ID ${rowId}?`)) {
                transactions = transactions.filter(row => row._row_id !== rowId);
                renderTable(); // Re-render
                setStatus(`Deleted row ID ${rowId}.`, 'info');
            }
        }

        // --- CSV Handling ---
        function handleCsvLoad(event) {
            const file = event.target.files[0];
            if (!file) {
                setStatus('No CSV file selected.', 'info');
                return;
            }
            setStatus(`Loading CSV: ${file.name}...`, 'info');

            Papa.parse(file, {
                header: true, // Assumes first row is header
                skipEmptyLines: true,
                complete: function(results) {
                    console.log("CSV Parsing complete:", results);
                    if (results.errors.length > 0) {
                         console.error("CSV Parsing Errors:", results.errors);
                         setStatus(`Errors encountered parsing CSV: ${results.errors.map(e=>e.message).join(', ')}`, 'error');
                         // Optionally show only first few errors
                    }
                    if (results.data && results.data.length > 0) {
                        processLoadedCsvData(results.data);
                    } else {
                         setStatus('CSV file is empty or could not be parsed.', 'error');
                    }
                    // Clear the file input value so the same file can be loaded again if needed
                     event.target.value = null;
                },
                error: function(error) {
                    console.error("CSV Parsing Failed:", error);
                    setStatus(`Failed to parse CSV file: ${error.message}`, 'error');
                     event.target.value = null;
                }
            });
        }

        function processLoadedCsvData(csvData) {
            const loadedTransactions = [];
            let loadedRowCount = 0;
            rowCounter = 0; // Reset row counter when loading

             // Normalize headers (lowercase, trim) for matching
             const headerMap = {};
             if (csvData.length > 0) {
                  Object.keys(csvData[0]).forEach(h => headerMap[h.toLowerCase().trim()] = h);
             }
            const findHeader = (possibleNames) => {
                 for (const name of possibleNames) {
                     if (headerMap[name]) return headerMap[name];
                 }
                 return null;
             }
            // Define expected column names (lowercase) and their target property
             const columnMapping = {
                 Date: findHeader(['date']),
                 Category: findHeader(['category', 'account']),
                 Amount: findHeader(['amount', 'value', 'total']),
                 TaxCode: findHeader(['tax code', 'taxcode', 'gst code', 'gstcode']),
                 Payee: findHeader(['payee', 'contact', 'name', 'supplier']),
                 Reference: findHeader(['reference', 'ref', 'ref no', 'ref number']),
                 Description: findHeader(['description', 'memo', 'details'])
             };

            csvData.forEach(row => {
                rowCounter++;
                const newRow = { _row_id: rowCounter };
                let hasRequired = true;

                // Map data using columnMapping
                for (const targetProp in columnMapping) {
                     const csvHeader = columnMapping[targetProp];
                     const rawValue = csvHeader ? (row[csvHeader] || '').trim() : '';

                    if (targetProp === 'Date') {
                         // Attempt to parse date (assuming various common formats might appear in CSV)
                         // PapaParse doesn't auto-detect dates well. Simple YYYY-MM-DD or DD/MM/YYYY checks?
                         let parsedDate = null;
                         if (rawValue.match(/^\d{4}-\d{2}-\d{2}$/)) { // YYYY-MM-DD
                              parsedDate = rawValue;
                         } else if (rawValue.match(/^\d{1,2}\/\d{1,2}\/\d{4}$/)) { // DD/MM/YYYY -> YYYY-MM-DD
                              const parts = rawValue.split('/');
                              if(parts.length === 3) {
                                   parsedDate = `${parts[2]}-${parts[1].padStart(2,'0')}-${parts[0].padStart(2,'0')}`;
                              }
                         } // Add more formats if needed
                         newRow[targetProp] = parsedDate;
                         if (!parsedDate) hasRequired = false; // Date is required
                    } else if (targetProp === 'Amount') {
                         try {
                             const numVal = parseFloat(rawValue.replace(/[^0-9.-]+/g,"")); // Clean number string
                             newRow[targetProp] = isNaN(numVal) ? null : Math.abs(numVal); // Store positive number or null
                             if (newRow[targetProp] === null) hasRequired = false; // Amount is required
                         } catch { newRow[targetProp] = null; hasRequired = false; }
                    } else {
                         newRow[targetProp] = rawValue;
                         // Check other required fields
                         if ((targetProp === 'Category' || targetProp === 'TaxCode') && !rawValue) {
                              hasRequired = false;
                         }
                    }
                }

                if (!hasRequired) {
                     console.warn(`Skipping loaded row ${rowCounter} due to missing/invalid required fields (Date, Category, Amount, TaxCode):`, row);
                } else {
                     loadedTransactions.push(newRow);
                     loadedRowCount++;
                }

            });

            if (loadedRowCount > 0) {
                transactions = loadedTransactions; // Replace current data
                renderTable();
                setStatus(`Successfully loaded ${loadedRowCount} rows from CSV. ${csvData.length - loadedRowCount} rows skipped.`, 'success');
            } else {
                setStatus('No valid transaction rows found in the CSV matching required columns.', 'error');
            }
        }

        function saveCsv() {
            if (transactions.length === 0) {
                setStatus("No data in the table to save.", "error");
                return;
            }
            setStatus("Generating CSV...", "info");

             // Define headers for CSV based on internal keys (excluding _row_id)
            const headers = ["Date", "Category", "Amount", "TaxCode", "Payee", "Reference", "Description"];
            const csvData = transactions.map(row => {
                 const csvRow = {};
                 headers.forEach(headerKey => {
                      let value = row[headerKey];
                       // Format date back to YYYY-MM-DD for consistency? Or keep internal format?
                      if (headerKey === 'Amount' && typeof value === 'number') {
                           value = value.toFixed(2); // Format amount
                      }
                      csvRow[headerKey] = value !== null && value !== undefined ? value : ''; // Ensure empty strings for null/undefined
                 });
                 return csvRow;
             });


            try {
                 const csvString = Papa.unparse(csvData, { header: true });
                 triggerDownload(csvString, `transactions_${new Date().toISOString().split('T')[0]}.csv`, 'text/csv;charset=utf-8;');
                 setStatus(`CSV file generated.`, 'success');
            } catch (error) {
                 console.error("CSV Unparsing Failed:", error);
                 setStatus(`Error generating CSV: ${error.message}`, 'error');
            }
        }

        // --- OFX Generation ---
        async function generateFitid(dateStr, amountStr, payeeStr, memoStr, uniqueId) {
            const inputString = `${dateStr}-${amountStr}-${payeeStr}-${memoStr}-${uniqueId}`;
            try {
                const encoder = new TextEncoder();
                const data = encoder.encode(inputString);
                // Use Web Crypto API for SHA-1 (async)
                const hashBuffer = await crypto.subtle.digest('SHA-1', data);
                // Convert ArrayBuffer to hex string
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                return hashHex.substring(0, 32); // Limit to 32 chars (typical OFX limit)
            } catch (error) {
                 console.error("SHA-1 Hashing failed:", error);
                 // Fallback to simple non-crypto hash if Web Crypto fails (less ideal)
                 let hash = 0;
                 for (let i = 0; i < inputString.length; i++) {
                     hash = ((hash << 5) - hash) + inputString.charCodeAt(i);
                     hash |= 0; // Convert to 32bit integer
                 }
                 return Math.abs(hash).toString(16).padStart(8, '0').substring(0, 32);
             }
        }

        function formatOfxDate(dateString) { // Expects YYYY-MM-DD
             if (typeof dateString === 'string' && dateString.match(/^\d{4}-\d{2}-\d{2}$/)) {
                 return dateString.replace(/-/g, ''); // YYYYMMDD
             }
             // Handle Date objects if they sneak in
             if(dateString instanceof Date) {
                  try { return dateString.toISOString().substring(0,10).replace(/-/g,''); } catch { return '';}
             }
             return ''; // Invalid format
        }

        async function generateOFX() {
            setStatus("Generating OFX...", "info");

            // 1. Validate Config
            const config = {
                bankId: bankIdInput.value.trim(),
                accountId: accountIdInput.value.trim(),
                accountType: accountTypeSelect.value,
                currency: currencyInput.value.trim().toUpperCase(),
                org: orgInput.value.trim(),
                fid: fidInput.value.trim(),
                language: 'ENG' // Hardcoded for now
            };
            if (!config.bankId || !config.accountId || !config.currency) {
                setStatus("Error: Please fill required configuration fields (Bank ID, Account ID, Currency).", "error");
                return;
            }

            // 2. Filter and Sort Valid Transactions
            const validTransactions = transactions.filter(row =>
                row.Date && row.Category && row.Amount !== undefined && row.Amount !== null && row.TaxCode
            );

            if (validTransactions.length === 0) {
                setStatus("Error: No valid transactions with required fields (Date, Category, Amount, Tax Code) to convert.", "error");
                return;
            }

            // Sort by date (important for OFX)
            validTransactions.sort((a, b) => a.Date.localeCompare(b.Date)); // Assumes YYYY-MM-DD string comparison works

            let minDateStr = validTransactions[0].Date;
            let maxDateStr = validTransactions[validTransactions.length - 1].Date;

            // 3. Build OFX String Components
            const now = new Date();
            const dateNowStr = now.toISOString().replace(/[-:.]/g, '').substring(0, 14); // YYYYMMDDHHMMSS
            const dateNowShortStr = dateNowStr.substring(0, 8); // YYYYMMDD
            const newFileUid = crypto.randomUUID ? crypto.randomUUID() : `MANUAL-${Date.now()}`; // Unique file ID
            const trnuid = crypto.randomUUID ? crypto.randomUUID() : `MANUAL-TRN-${Date.now()}`; // Unique transaction response ID

             // Careful: formatOfxDate expects YYYY-MM-DD string
            const dtStart = formatOfxDate(minDateStr);
            const dtEnd = formatOfxDate(maxDateStr);

            let ofxBody = ""; // Store STMTTRN blocks

            // 4. Process Transactions into OFX Body
            for (const row of validTransactions) {
                try {
                     const dtPostedStr = formatOfxDate(row.Date);
                     if (!dtPostedStr) {
                          console.warn("Skipping row with invalid date format:", row);
                          continue; // Skip if date is bad
                     }
                     const ofxAmount = -Math.abs(parseFloat(row.Amount)); // Ensure negative
                     const payee = (row.Payee || 'Unknown Payee').substring(0, 32); // Limit length
                     const memoParts = [ `Cat: ${row.Category}`, `Tax: ${row.TaxCode}` ];
                     if (row.Reference) memoParts.push(`Ref: ${row.Reference}`);
                     if (row.Description) memoParts.push(`Desc: ${row.Description}`);
                     const memo = memoParts.join(' | ').substring(0, 255); // Limit length

                    // Use consistent strings for FITID generation
                     const amtStrFitid = ofxAmount.toFixed(2);
                     const fitid = await generateFitid(dtPostedStr, amtStrFitid, payee, memo, row._row_id);

                     // Using template literals for clarity
                     ofxBody += `
<STMTTRN>
<TRNTYPE>DEBIT</TRNTYPE>
<DTPOSTED>${dtPostedStr}</DTPOSTED>
<TRNAMT>${ofxAmount.toFixed(2)}</TRNAMT>
<FITID>${fitid}</FITID>
<NAME>${payee.replace(/[&<>"']/g, '')}</NAME>
<MEMO>${memo.replace(/[&<>"']/g, '')}</MEMO>
</STMTTRN>`;
                 } catch (error) {
                      console.error(`Error processing row ID ${row._row_id} for OFX:`, error, row);
                      setStatus(`Error on row ID ${row._row_id}. Check console.`, "error");
                      // Optionally stop entire process on error
                 }
            }

            if (!ofxBody) {
                 setStatus("Error: Failed to generate any transaction data for OFX.", "error");
                 return;
            }


            // 5. Assemble Full OFX String
            const ofxContent = `OFXHEADER:100
DATA:OFXSGML
VERSION:102
SECURITY:NONE
ENCODING:USASCII
CHARSET:1252
COMPRESSION:NONE
OLDFILEUID:NONE
NEWFILEUID:${newFileUid}

<OFX>
<SIGNONMSGSRSV1>
<SONRS>
<STATUS>
<CODE>0</CODE>
<SEVERITY>INFO</SEVERITY>
</STATUS>
<DTSERVER>${dateNowStr}</DTSERVER>
<LANGUAGE>${config.language}</LANGUAGE>
</SONRS>
</SIGNONMSGSRSV1>
<BANKMSGSRSV1>
<STMTTRNRS>
<TRNUID>${trnuid}</TRNUID>
<STATUS>
<CODE>0</CODE>
<SEVERITY>INFO</SEVERITY>
</STATUS>
<STMTRS>
<CURDEF>${config.currency}</CURDEF>
<BANKACCTFROM>
<BANKID>${config.bankId.replace(/[&<>"']/g, '')}</BANKID>
<ACCTID>${config.accountId.replace(/[&<>"']/g, '')}</ACCTID>
<ACCTTYPE>${config.accountType}</ACCTTYPE>
</BANKACCTFROM>
<BANKTRANLIST>
<DTSTART>${dtStart}</DTSTART>
<DTEND>${dtEnd}</DTEND>${ofxBody}
</BANKTRANLIST>
<LEDGERBAL>
<BALAMT>0.00</BALAMT>
<DTASOF>${dtEnd || dateNowShortStr}</DTASOF>
</LEDGERBAL>
</STMTRS>
</STMTTRNRS>
</BANKMSGSRSV1>
</OFX>
`;

            // 6. Trigger Download
            const filename = `ofx_export_${dateNowShortStr}.ofx`;
            triggerDownload(ofxContent, filename, 'application/ofx');
            setStatus(`Successfully generated OFX file for ${validTransactions.length} transactions.`, 'success');
        }

        // --- File Download Trigger ---
        function triggerDownload(content, filename, contentType) {
            const blob = new Blob([content], { type: contentType });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a); // Firefox requirement
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url); // Free up memory
        }

        // --- Event Listeners Initialization ---
        addRowBtn.addEventListener('click', addRow);
        convertOfxBtn.addEventListener('click', generateOFX);
        csvFileInput.addEventListener('change', handleCsvLoad);
        saveCsvBtn.addEventListener('click', saveCsv);


        // --- Initial Setup ---
        // Check data lists
        if (chartOfAccounts.length === 0 || taxCodes.length === 0) {
            setStatus("Warning: Account or Tax Code lists are empty. Edit the HTML file's script section!", "error");
        }
        // Initial table render (might show "empty" message)
        renderTable();

        // --- Theme Management ---
        // Set theme by adding data-theme attribute to body
        function setTheme(themeName) {
            if (themeName === 'default') {
                document.body.removeAttribute('data-theme');
            } else {
                document.body.setAttribute('data-theme', themeName);
            }
            localStorage.setItem('ofx-converter-theme', themeName);
        }

        // Listen for theme messages from parent window
        window.addEventListener('message', function(event) {
            if (event.data && event.data.action === 'setTheme') {
                setTheme(event.data.theme);
                // Theme selector removed, just apply the theme
            }
        });

    </script>

</body>
</html>