// --- Core Conversion Functions ---
/**
 * Converts a time string in HH:MM format (00:00 to 23:59) to a decimal number.
 * Assumes input string is pre-validated/formatted.
 * @param {string} timeString - The time string (e.g., "07:36", "23:59").
 * @returns {{value: number}|{error: string}} Object with 'value' or 'error'.
 */
function timeToDecimal(timeString) {
    // ... (Keep this function as is from previous version) ...
     if (!timeString || typeof timeString !== 'string') {
        return { error: "Input is empty." };
    }
    const timeRegex = /^(\d{1,2}):([0-5]\d)$/;
    const match = timeString.match(timeRegex);
    if (!match) {
        return { error: "Invalid time format. Use HH:MM." };
    }
    const hours = parseInt(match[1], 10);
    const minutes = parseInt(match[2], 10);
    if (isNaN(hours) || isNaN(minutes) || hours < 0 || minutes < 0 || minutes > 59) {
         return { error: "Invalid hour or minute value (minutes 00-59)." };
    }
    if (hours >= 24) {
        return { error: "Time cannot be 24:00 or greater. Use 00:00 to 23:59." };
    }
    const decimalValue = hours + (minutes / 60);
    return { value: Number(decimalValue.toFixed(4)) };
}

/**
 * Converts a decimal time value (< 24) to HH:MM format.
 * Assumes input decimalValue is already validated (is a number, < 24, >= 0).
 * @param {number} decimalValue - The decimal value (e.g., 7.6).
 * @returns {{value: string}|{error: string}} Object with 'value' (HH:MM string) or 'error'.
 */
function decimalToTime(decimalValue) {
    // ... (Keep this function as is) ...
     if (decimalValue === null || decimalValue === undefined || isNaN(decimalValue)) {
         return { error: "Internal error: Invalid decimal value received." };
    }
     if (decimalValue < 0) {
        return { error: "Decimal value cannot be negative." };
    }
    const totalMinutes = Math.round(decimalValue * 60);
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;
    const formattedHours = String(hours).padStart(2, '0');
    const formattedMinutes = String(minutes).padStart(2, '0');
    return { value: `${formattedHours}:${formattedMinutes}` };
}


// --- DOM Elements ---
// ... (Keep existing selections) ...
const timeInput = document.getElementById('timeInput');
const calculateDecimalBtn = document.getElementById('calculateDecimalBtn');
const decimalResultSpan = document.getElementById('decimalResult');
const errorMessageTimeDiv = document.getElementById('error-message-time');

const decimalInput = document.getElementById('decimalInput');
const calculateTimeBtn = document.getElementById('calculateTimeBtn');
const timeResultSpan = document.getElementById('timeResult');
const errorMessageDecimalDiv = document.getElementById('error-message-decimal');

const tabButtons = document.querySelectorAll('.tab-button');
const tabPanels = document.querySelectorAll('.tab-panel');

// *** Add Checkbox Selections ***
const realtimeCheckTime = document.getElementById('realtimeCheckTime');
const realtimeCheckDecimal = document.getElementById('realtimeCheckDecimal');

// Theme selector removed - now controlled by dashboard


// --- Input Formatting and Filtering ---

/**
 * Formats the Time Input (HH:MM) field and optionally triggers real-time calculation.
 * @param {Event} event The input event.
 */
function formatTimeInput(event) {
    const input = event.target;
    let value = input.value;
    const originalCursorPos = input.selectionStart;
    let handledCursorPos = originalCursorPos;

    // --- Formatting Logic (Keep as is from previous version) ---
    let filteredValue = value.replace(/[^\d:]/g, '');
    const colonIndex = filteredValue.indexOf(':');
    if (colonIndex !== -1) {
        filteredValue = filteredValue.substring(0, colonIndex + 1) + filteredValue.substring(colonIndex + 1).replace(/:/g, '');
        if (colonIndex === 0) {
             filteredValue = filteredValue.substring(1);
             handledCursorPos = Math.max(0, handledCursorPos -1);
        }
         else if (colonIndex > 2) {
             filteredValue = filteredValue.substring(0, colonIndex) + filteredValue.substring(colonIndex + 1);
         }
    }
    let hoursStr = filteredValue;
    let minutesStr = '';
    if (colonIndex !== -1) {
        hoursStr = filteredValue.substring(0, colonIndex);
        minutesStr = filteredValue.substring(colonIndex + 1);
    } else {
        if (filteredValue.length > 2) {
            hoursStr = filteredValue.substring(0, 2);
            minutesStr = filteredValue.substring(2);
            filteredValue = hoursStr + ':' + minutesStr;
             if (originalCursorPos >= 2) {
                 handledCursorPos++;
             }
        } else {
             hoursStr = filteredValue;
             minutesStr = '';
        }
    }
    if (hoursStr.length > 2) hoursStr = hoursStr.slice(0, 2);
    if (minutesStr.length > 2) minutesStr = minutesStr.slice(0, 2);
    let finalFormattedValue = hoursStr;
     if (filteredValue.includes(':') || finalFormattedValue.length === 2 && minutesStr.length > 0) {
         finalFormattedValue += ':' + minutesStr;
    } else if (minutesStr.length > 0 && finalFormattedValue.length < 2) {
         finalFormattedValue += ':' + minutesStr;
    }
     else {
         finalFormattedValue = hoursStr;
     }
    if (finalFormattedValue.length > 5) {
         finalFormattedValue = finalFormattedValue.slice(0, 5);
    }
    // --- End Formatting Logic ---


    // Update input value and manage cursor (Keep as is)
    if (input.value !== finalFormattedValue) {
        input.value = finalFormattedValue;
        try {
             const newPos = Math.max(0, Math.min(handledCursorPos, finalFormattedValue.length));
             input.setSelectionRange(newPos, newPos);
         } catch (e) {
             console.error("Cursor position handling failed:", e);
         }
    }

    // Clear previous errors (Keep as is)
    clearError(errorMessageTimeDiv, decimalResultSpan, '--');

    // *** Trigger real-time calculation if checkbox is checked ***
    if (realtimeCheckTime.checked) {
        // Use a slight delay (optional, but can prevent firing on intermediate states)
        // setTimeout(handleTimeToDecimal, 50);
        handleTimeToDecimal(); // Or call directly
    }
}


/**
 * Filters the Decimal Input field and optionally triggers real-time calculation.
 * @param {Event} event The input event.
 */
function filterDecimalInput(event) {
    const input = event.target;
    const originalValue = input.value;
    const originalCursorPos = input.selectionStart;

    // --- Filtering Logic (Keep as is) ---
    let filteredValue = originalValue.replace(/[^\d.]/g, '');
    const dotIndex = filteredValue.indexOf('.');
    if (dotIndex !== -1) {
        filteredValue = filteredValue.substring(0, dotIndex + 1) + filteredValue.substring(dotIndex + 1).replace(/\./g, '');
    }
    // --- End Filtering Logic ---

    // Update input value and manage cursor (Keep as is)
    if (input.value !== filteredValue) {
         input.value = filteredValue;
         try {
             const lengthDiff = filteredValue.length - originalValue.length;
             let newCursorPos = originalCursorPos + lengthDiff;
             newCursorPos = Math.max(0, Math.min(newCursorPos, filteredValue.length));
             input.setSelectionRange(newCursorPos, newCursorPos);
         } catch (e) {
             console.error("Cursor position handling failed:", e);
         }
    }

    // Clear previous errors (Keep as is)
    clearError(errorMessageDecimalDiv, timeResultSpan, '--:--');

    // *** Trigger real-time calculation if checkbox is checked ***
    if (realtimeCheckDecimal.checked) {
       // Use a slight delay (optional)
       // setTimeout(handleDecimalToTime, 50);
       handleDecimalToTime(); // Or call directly
    }
}

/** Helper to clear error messages and reset result display. */
function clearError(errorDiv, resultSpan, defaultResult) {
     // ... (Keep this function as is) ...
      if (errorDiv.textContent) {
        errorDiv.textContent = '';
    }
    if (resultSpan.style.color === 'rgb(220, 53, 69)' || resultSpan.textContent !== defaultResult) {
        resultSpan.textContent = defaultResult;
        resultSpan.style.color = '#28a745';
    }
}


// --- Tab Switching Logic ---
function switchTab(event) {
    // ... (Keep this function as is) ...
    const clickedTab = event.currentTarget;
    const targetPanelId = clickedTab.getAttribute('aria-controls');
    const targetPanel = document.getElementById(targetPanelId);

    tabButtons.forEach(tab => {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
        tab.setAttribute('tabindex', '-1');
    });
    tabPanels.forEach(panel => {
        panel.classList.remove('active');
        panel.hidden = true;
    });

    clickedTab.classList.add('active');
    clickedTab.setAttribute('aria-selected', 'true');
    clickedTab.removeAttribute('tabindex');

    if (targetPanel) {
        targetPanel.classList.add('active');
        targetPanel.hidden = false;
    }
}

// --- Event Handlers for Calculations ---
/** Handles the Time to Decimal calculation trigger. Includes validation. */
function handleTimeToDecimal() {
    // Only clear errors if NOT in real-time mode (real-time clears on input)
    if (!realtimeCheckTime.checked) {
        clearError(errorMessageTimeDiv, decimalResultSpan, '--');
    }

    const inputValue = timeInput.value;
    const result = timeToDecimal(inputValue); // Includes 24hr check

    if (result.error) {
        errorMessageTimeDiv.textContent = result.error;
        decimalResultSpan.style.color = '#dc3545';
        decimalResultSpan.textContent = 'Error';
        if (!/^\d{1,2}:[0-5]\d$/.test(inputValue)) {
             errorMessageTimeDiv.textContent = "Incomplete time. Please enter HH:MM.";
        }
    } else {
        errorMessageTimeDiv.textContent = ''; // Ensure error cleared on success
        decimalResultSpan.textContent = result.value.toFixed(2);
        decimalResultSpan.style.color = '#28a745';
    }
}

/** Handles the Decimal to Time calculation trigger. Includes validation. */
function handleDecimalToTime() {
     // Only clear errors if NOT in real-time mode (real-time clears on input)
     if (!realtimeCheckDecimal.checked) {
         clearError(errorMessageDecimalDiv, timeResultSpan, '--:--');
     }

    const inputValue = decimalInput.value.trim();

    // --- Validation (Keep as is) ---
    if (inputValue === '') {
        errorMessageDecimalDiv.textContent = "Input is empty.";
        timeResultSpan.style.color = '#dc3545'; timeResultSpan.textContent = 'Error'; return;
    }
    if (inputValue === '.') {
         errorMessageDecimalDiv.textContent = "Invalid input. Please enter a number.";
         timeResultSpan.style.color = '#dc3545'; timeResultSpan.textContent = 'Error'; return;
    }
    const decimalValue = Number(inputValue);
    if (isNaN(decimalValue)) {
        errorMessageDecimalDiv.textContent = "Invalid input. Please enter a number.";
         timeResultSpan.style.color = '#dc3545'; timeResultSpan.textContent = 'Error'; return;
    }
    if (decimalValue >= 24) {
        errorMessageDecimalDiv.textContent = "Decimal value must be less than 24.00.";
        timeResultSpan.style.color = '#dc3545'; timeResultSpan.textContent = 'Error'; return;
    }
     if (decimalValue < 0) {
        errorMessageDecimalDiv.textContent = "Decimal value cannot be negative.";
        timeResultSpan.style.color = '#dc3545'; timeResultSpan.textContent = 'Error'; return;
    }
    // --- End Validation ---

    const result = decimalToTime(decimalValue);
    if (result.error) {
        errorMessageDecimalDiv.textContent = result.error;
        timeResultSpan.style.color = '#dc3545';
        timeResultSpan.textContent = 'Error';
    } else {
        errorMessageDecimalDiv.textContent = ''; // Ensure error cleared on success
        timeResultSpan.textContent = result.value;
        timeResultSpan.style.color = '#28a745';
    }
}


// --- Event Listeners ---

// Tab Switching Listener
tabButtons.forEach(button => {
    button.addEventListener('click', switchTab);
});

// Input Formatting/Filtering & Real-time Calculation Listeners
timeInput.addEventListener('input', formatTimeInput);
decimalInput.addEventListener('input', filterDecimalInput);

// --- Checkbox Change Listeners ---
realtimeCheckTime.addEventListener('change', (event) => {
    const isChecked = event.target.checked;
    calculateDecimalBtn.disabled = isChecked; // Disable button if checked

    // If checked, trigger an initial calculation
    if (isChecked) {
        handleTimeToDecimal();
    } else {
        // Optional: Clear results/errors when disabling real-time?
        // clearError(errorMessageTimeDiv, decimalResultSpan, '--');
    }
});

realtimeCheckDecimal.addEventListener('change', (event) => {
    const isChecked = event.target.checked;
    calculateTimeBtn.disabled = isChecked; // Disable button if checked

     // If checked, trigger an initial calculation
    if (isChecked) {
        handleDecimalToTime();
    } else {
        // Optional: Clear results/errors when disabling real-time?
       // clearError(errorMessageDecimalDiv, timeResultSpan, '--:--');
    }
});


// Calculation Trigger Listeners (Button Clicks - will only fire if not disabled)
calculateDecimalBtn.addEventListener('click', handleTimeToDecimal);
calculateTimeBtn.addEventListener('click', handleDecimalToTime);

// Calculation Trigger Listeners (Enter Key - respect button disabled state)
timeInput.addEventListener('keypress', (event) => {
    if ((event.key === 'Enter' || event.keyCode === 13) && !calculateDecimalBtn.disabled) {
        event.preventDefault();
        handleTimeToDecimal();
    }
});
decimalInput.addEventListener('keypress', (event) => {
    if ((event.key === 'Enter' || event.keyCode === 13) && !calculateTimeBtn.disabled) {
        event.preventDefault();
        handleDecimalToTime();
    }
});

// --- Theme Management ---
// Set theme by adding data-theme attribute to body
function setTheme(themeName) {
    if (themeName === 'default') {
        document.body.removeAttribute('data-theme');
    } else {
        document.body.setAttribute('data-theme', themeName);
    }
    localStorage.setItem('time-calculator-theme', themeName);
}

// Listen for theme messages from parent window
window.addEventListener('message', function(event) {
    if (event.data && event.data.action === 'setTheme') {
        setTheme(event.data.theme);
        // Theme selector removed, just apply the theme
    }
});

// Initialize view on load
document.addEventListener('DOMContentLoaded', () => {
    // ... (Keep DOMContentLoaded logic as is) ...
    const activeTab = document.querySelector('.tab-button.active');
    if (activeTab) {
        const activePanelId = activeTab.getAttribute('aria-controls');
        const activePanel = document.getElementById(activePanelId);
        if (activePanel) {
            activePanel.hidden = false;
            activePanel.classList.add('active');
        }
        tabPanels.forEach(panel => {
             if (panel !== activePanel) {
                 panel.hidden = true;
                 panel.classList.remove('active');
             }
        });
    }
    // Ensure buttons are enabled initially (matching default checkbox state)
    calculateDecimalBtn.disabled = realtimeCheckTime.checked;
    calculateTimeBtn.disabled = realtimeCheckDecimal.checked;

    // Theme is now controlled by dashboard
});