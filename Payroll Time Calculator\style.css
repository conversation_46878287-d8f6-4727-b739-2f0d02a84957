/* Theme Colors - Default Light Blue */
:root {
    /* Light Blue Theme (Default) */
    --primary-color: #3498db;       /* Main blue */
    --primary-dark: #2980b9;        /* Darker blue */
    --secondary-color: #2ecc71;     /* Green */
    --secondary-dark: #27ae60;      /* Darker green */
    --accent-color: #e74c3c;        /* Red accent */
    --accent-dark: #c0392b;         /* Darker red */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --bg-color: #ffffff;            /* Background color */
    --text-color: #333333;          /* Text color */
    --heading-color: #0056b3;       /* Heading color */
    --button-color: #007bff;        /* Button color */
    --button-hover: #0056b3;        /* Button hover color */
    --result-color: #28a745;        /* Result color */
    --error-color: #dc3545;         /* Error color */
}

/* Dark Theme */
[data-theme="dark"] {
    --primary-color: #4dabf7;       /* Brighter blue for dark mode */
    --primary-dark: #339af0;        /* Slightly lighter blue */
    --secondary-color: #51cf66;     /* Brighter green */
    --secondary-dark: #40c057;      /* Slightly lighter green */
    --accent-color: #ff6b6b;        /* Brighter red */
    --accent-dark: #fa5252;         /* Slightly lighter red */
    --neutral-light: #343a40;       /* Dark background */
    --neutral-medium: #495057;      /* Medium dark background */
    --neutral-dark: #dee2e6;        /* Light text */
    --shadow: rgba(0, 0, 0, 0.3);   /* Darker shadow */
    --bg-color: #212529;            /* Dark background */
    --text-color: #f8f9fa;          /* Light text */
    --heading-color: #4dabf7;       /* Heading color */
    --button-color: #4dabf7;        /* Button color */
    --button-hover: #339af0;        /* Button hover color */
    --result-color: #51cf66;        /* Result color */
    --error-color: #ff6b6b;         /* Error color */
}

/* Purple Theme */
[data-theme="purple"] {
    --primary-color: #9775fa;       /* Main purple */
    --primary-dark: #845ef7;        /* Darker purple */
    --secondary-color: #5c7cfa;     /* Blue secondary */
    --secondary-dark: #4c6ef5;      /* Darker blue */
    --accent-color: #ff8787;        /* Coral accent */
    --accent-dark: #ff6b6b;         /* Darker coral */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --bg-color: #ffffff;            /* Background color */
    --text-color: #333333;          /* Text color */
    --heading-color: #9775fa;       /* Heading color */
    --button-color: #9775fa;        /* Button color */
    --button-hover: #845ef7;        /* Button hover color */
    --result-color: #5c7cfa;        /* Result color */
    --error-color: #ff8787;         /* Error color */
}

/* Green Theme */
[data-theme="green"] {
    --primary-color: #40c057;       /* Main green */
    --primary-dark: #37b24d;        /* Darker green */
    --secondary-color: #4dabf7;     /* Blue secondary */
    --secondary-dark: #339af0;      /* Darker blue */
    --accent-color: #ff922b;        /* Orange accent */
    --accent-dark: #fd7e14;         /* Darker orange */
    --neutral-light: #f8f9fa;       /* Light background */
    --neutral-medium: #e9ecef;      /* Medium background */
    --neutral-dark: #343a40;        /* Dark text */
    --shadow: rgba(0, 0, 0, 0.1);   /* Shadow color */
    --bg-color: #ffffff;            /* Background color */
    --text-color: #333333;          /* Text color */
    --heading-color: #40c057;       /* Heading color */
    --button-color: #40c057;        /* Button color */
    --button-hover: #37b24d;        /* Button hover color */
    --result-color: #40c057;        /* Result color */
    --error-color: #ff922b;         /* Error color */
}

/* Dark Blue Theme */
[data-theme="dark-blue"] {
    --primary-color: #228be6;       /* Main blue */
    --primary-dark: #1c7ed6;        /* Darker blue */
    --secondary-color: #15aabf;     /* Teal secondary */
    --secondary-dark: #1098ad;      /* Darker teal */
    --accent-color: #f76707;        /* Orange accent */
    --accent-dark: #e8590c;         /* Darker orange */
    --neutral-light: #343a40;       /* Dark background */
    --neutral-medium: #495057;      /* Medium dark background */
    --neutral-dark: #dee2e6;        /* Light text */
    --shadow: rgba(0, 0, 0, 0.3);   /* Darker shadow */
    --bg-color: #1e2a3a;            /* Dark blue background */
    --text-color: #f8f9fa;          /* Light text */
    --heading-color: #228be6;       /* Heading color */
    --button-color: #228be6;        /* Button color */
    --button-hover: #1c7ed6;        /* Button hover color */
    --result-color: #15aabf;        /* Result color */
    --error-color: #f76707;         /* Error color */
}

/* Basic Reset & Font */
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--neutral-light);
    color: var(--text-color);
    display: flex;
    justify-content: center;
    align-items: flex-start;
    min-height: 100vh;
    padding: 40px 20px;
    margin: 0;
    box-sizing: border-box;
    transition: all 0.3s ease;
}

/* Calculator Container Styling */
.calculator-container {
    background-color: var(--bg-color);
    padding: 30px 40px;
    border-radius: 8px;
    box-shadow: 0 4px 12px var(--shadow);
    text-align: center;
    max-width: 480px;
    width: 100%;
    overflow: hidden; /* Contain rounded corners if added */
}

h1 {
    color: var(--heading-color);
    margin-bottom: 25px;
    font-weight: 700;
    font-size: 1.6em;
    text-align: center;
}

/* --- Tab Styles --- */
.tab-container {
    display: flex;
    margin-bottom: 25px;
    /* Removed border-bottom here, will rely on panel or button styles */
}

.tab-button {
    padding: 12px 20px; /* Adjusted padding for better fill */
    cursor: pointer;
    border: none;
    background-color: var(--neutral-medium); /* Light grey background for inactive tabs */
    /* border-bottom: 3px solid transparent; */ /* Removed border indicator */
    /* margin-bottom: -2px; */ /* Removed overlap */
    font-size: 1em;
    font-weight: 500;
    color: var(--primary-color); /* Blue text for inactive tabs */
    transition: background-color 0.2s ease-in-out, color 0.2s ease-in-out;
    flex-grow: 1;
    text-align: center;
    border-radius: 6px 6px 0 0; /* Rounded top corners */
    border: 1px solid var(--neutral-medium); /* Subtle border for definition */
    border-bottom: none; /* Remove bottom border to merge with panel area visually */
    margin-right: -1px; /* Overlap borders slightly */
}

.tab-button:last-child {
    margin-right: 0; /* No overlap for the last button */
}

/* Style for hovered INACTIVE tabs */
.tab-button:not(.active):hover {
    background-color: var(--neutral-medium); /* Slightly darker grey on hover */
    color: var(--primary-dark);
}

/* --- Active Tab Style --- */
.tab-button.active {
    background-color: var(--button-color); /* Primary blue background */
    color: #ffffff;            /* White text */
    font-weight: 700;
    /* border-bottom-color: transparent; */ /* Not needed anymore */
    border-color: var(--button-color); /* Match border color */
    position: relative; /* Needed for potential z-index */
    z-index: 1; /* Ensure active tab border overlaps inactive */
}

/* Hover style for the ACTIVE tab (optional, could just keep it solid) */
.tab-button.active:hover {
     background-color: var(--button-hover); /* Darker blue on hover */
     border-color: var(--button-hover);
}

.tab-button:focus {
    outline: none; /* Remove default outline */
    box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.4); /* Custom focus ring */
    z-index: 2; /* Ensure focus ring is visible */
    position: relative; /* Ensure z-index applies */
}

/* --- Tab Panel Styles --- */
.tab-content-container {
    margin-top: 0; /* Removed margin as tabs connect directly */
    border: 1px solid var(--neutral-medium); /* Add border to the container */
    border-top: none; /* Remove top border as tabs cover it */
    border-radius: 0 0 6px 6px; /* Round bottom corners */
    clear: both; /* Ensure it clears the floated/flexed tabs */
    position: relative; /* To contain positioned elements */
    background-color: var(--bg-color); /* Ensure background */
    margin-top: -1px; /* Overlap the tab container slightly if needed */
}

.tab-panel {
    padding: 25px 20px; /* Adjusted padding */
    animation: fadeIn 0.3s ease-in-out;
    /* border-top: none; */ /* Removed */
}

.tab-panel:not(.active) {
    display: none;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* --- Content Inside Panels (Mostly unchanged) --- */

.description {
    color: var(--text-color);
    font-size: 0.9em;
    margin-bottom: 25px;
    line-height: 1.5;
    text-align: left; /* Align description left */
}

.input-group {
    margin-bottom: 20px;
    text-align: left;
}

.input-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.input-group input[type="text"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--neutral-medium);
    border-radius: 5px;
    font-size: 1em;
    box-sizing: border-box;
    transition: border-color 0.2s ease-in-out;
    background-color: var(--bg-color);
    color: var(--text-color);
}

.input-group input[type="text"]:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(var(--primary-color), 0.2);
}

button#calculateDecimalBtn,
button#calculateTimeBtn {
    background-color: var(--button-color);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    font-size: 1em;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out, transform 0.1s ease;
    display: block;
    width: 100%;
    margin-bottom: 25px;
}

button#calculateDecimalBtn:hover,
button#calculateTimeBtn:hover {
    background-color: var(--button-hover);
}

button#calculateDecimalBtn:active,
button#calculateTimeBtn:active {
    transform: scale(0.98);
}

.result-area {
    margin-top: 20px;
    padding: 15px;
    background-color: var(--neutral-medium);
    border-radius: 5px;
    border: 1px solid var(--neutral-medium);
    text-align: center;
}

.result-area h3 {
    margin: 0 0 10px 0;
    font-size: 1.0em;
    color: var(--text-color);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-value {
    font-size: 1.8em;
    font-weight: 700;
    color: var(--result-color);
    display: block;
    min-height: 1.2em;
    line-height: 1.2;
}

.error-message {
    color: var(--error-color);
    font-weight: 500;
    margin-top: 15px;
    min-height: 1.2em;
    text-align: center;
    font-size: 0.9em;
}

/* ... (keep all existing styles) ... */

/* --- Checkbox Group Styling --- */
.checkbox-group {
    display: flex;
    align-items: center;
    justify-content: flex-start; /* Align to left */
    margin-top: -10px; /* Pull it up slightly */
    margin-bottom: 20px; /* Space before button */
    text-align: left;
}

.checkbox-group input[type="checkbox"] {
    margin-right: 8px; /* Space between checkbox and label */
    cursor: pointer;
    /* Optional: Scale checkbox slightly if desired */
    /* transform: scale(1.1); */
}

.checkbox-group label {
    font-size: 0.9em;
    color: var(--text-color);
    cursor: pointer;
    user-select: none; /* Prevent text selection on label click */
}

/* Style for disabled calculate buttons */
button:disabled {
    background-color: var(--neutral-medium); /* Grey background */
    cursor: not-allowed;
    opacity: 0.7; /* Slightly faded */
}

button:disabled:hover {
     background-color: var(--neutral-medium); /* Keep grey on hover */
}

/* Theme selector - Removed from individual tools */

/* End of styles */